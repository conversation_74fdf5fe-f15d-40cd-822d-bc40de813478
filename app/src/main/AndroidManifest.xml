<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/Theme.Yhkit2025"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Yhkit2025">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".OrderActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhkit2025">
        </activity>
        <activity
            android:name=".LogViewActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhkit2025" />
        <activity
            android:name=".LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.Yhkit2025" />

        <!-- 后台服务 -->
        <service
            android:name=".service.BackgroundService"
            android:enabled="true"
            android:exported="false" />

        <!-- 是否为Xposed模块 -->
        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <!-- 模块的简介（在框架中显示） -->
        <meta-data
            android:name="xposeddescription"
            android:value="永辉生活套件" />
        <!-- 模块最低支持的Api版本 一般填93即可 -->
        <meta-data
            android:name="xposedminversion"
            android:value="93" />
        <!-- 模块作用域 -->
        <meta-data
            android:name="xposedscope"
            android:resource="@array/xposedscope" />
        <!-- 模块是否启用XSharedPreferences -->
        <meta-data
            android:name="xposedsharedprefs"
            android:value="true" />
    </application>

</manifest>
