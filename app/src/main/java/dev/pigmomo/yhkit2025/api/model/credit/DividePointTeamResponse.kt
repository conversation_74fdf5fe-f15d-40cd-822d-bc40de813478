package dev.pigmomo.yhkit2025.api.model.credit

/**
 * 积分组队瓜分响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 组队瓜分数据
 * @property now 当前时间戳
 */
data class DividePointTeamResponse(
    val code: Int = 0,
    val message: String = "",
    val data: DividePointTeamData? = null,
    val now: Long = 0
)

/**
 * 积分组队瓜分数据类
 * @property title 活动标题
 * @property subTitle 活动子标题
 * @property endTime 结束时间戳
 * @property createTime 创建时间戳
 * @property teamStatus 团队状态
 * @property teamCode 团队码
 * @property description 活动描述
 * @property teamDetailList 团队成员列表
 * @property inviterReward 邀请者奖励
 * @property inviterRewardType 邀请者奖励类型
 * @property newMember 是否新成员
 * @property currentNewMember 当前用户是否是新成员
 * @property actionUrl 活动链接URL
 * @property hasActivity 是否有活动
 * @property teamSize 团队大小
 * @property teamCurrentFlag 当前用户是否在团队中
 * @property currentCredit 当前用户积分
 * @property currentRole 当前用户角色
 * @property newPersonAmount 新人金额
 * @property desc 描述信息
 * @property createTeamFlag 是否可以创建团队
 * @property activityEndTime 活动结束时间戳
 * @property hasTeam 是否已有团队
 * @property currentMemberImage 当前成员头像
 * @property shareTitle 分享标题
 * @property shareImg 分享图片
 */
data class DividePointTeamData(
    val title: String = "",
    val subTitle: String = "",
    val endTime: Long = 0,
    val createTime: Long? = null,
    val teamStatus: Int = 0,
    val teamCode: String = "",
    val description: String = "",
    val teamDetailList: List<TeamMember> = emptyList(),
    val inviterReward: String = "",
    val inviterRewardType: String = "",
    val newMember: Boolean = false,
    val currentNewMember: Boolean = false,
    val actionUrl: String = "",
    val hasActivity: Boolean = false,
    val teamSize: Int = 0,
    val teamCurrentFlag: Boolean = false,
    val currentCredit: Int? = null,
    val currentRole: Int = 0,
    val newPersonAmount: String? = null,
    val desc: String = "",
    val createTeamFlag: Boolean = false,
    val activityEndTime: Long = 0,
    val hasTeam: Boolean = false,
    val currentMemberImage: String? = null,
    val shareTitle: String = "",
    val shareImg: String = ""
)

/**
 * 团队成员数据类
 * @property memberId 成员ID
 * @property memberImage 成员头像
 * @property credit 积分数值
 * @property role 角色(0-队长，1-队员)
 * @property currentFlag 是否为当前用户
 * @property newMemberFlag 新成员标志(0-老用户，1-新用户)
 */
data class TeamMember(
    val memberId: Long = 0,
    val memberImage: String = "",
    val credit: Int = 0,
    val role: Int = 0,
    val currentFlag: Boolean = false,
    val newMemberFlag: Int = 0
) 