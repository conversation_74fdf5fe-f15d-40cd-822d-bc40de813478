package dev.pigmomo.yhkit2025.api.model.order

/**
 * 订单详情响应数据类
 * @property code 响应状态码，0表示成功
 * @property message 响应消息
 * @property data 订单详情数据
 * @property now 当前时间戳
 */
data class OrderDetailResponse(
    val code: Int = 0,
    val message: String = "",
    val data: OrderDetailData? = null,
    val now: Long = 0
)

/**
 * 订单详情数据类
 * @property status 订单状态
 * @property cashierswitch 收银开关
 * @property activityList 活动列表
 * @property nimbleIsland 灵活岛屿
 * @property statusmsg 状态消息
 * @property payendtime 支付结束时间
 * @property ordertype 订单类型
 * @property serviceaction 服务动作
 * @property showonlineservice 是否显示在线服务
 * @property pricedetail 价格详情
 * @property orderpayinfo 订单支付信息
 * @property baseinfo 基本信息
 * @property shopinfo 商店信息
 * @property productsinfo 商品信息
 * @property commentinfo 评论信息
 * @property statusinfo 状态信息
 * @property showmap 是否显示地图
 * @property servicecanrate 服务可评分
 * @property servicecanratetime 服务可评分时间
 * @property canupdatedelivery 是否可以更新配送
 * @property ispresale 是否预售
 * @property resourceLocation 资源位置
 * @property noTouchPics 无接触图片
 * @property orderDetailAfterSale 订单售后详情
 * @property addressTop 地址顶部
 * @property orderMsgOpen 订单消息开启
 * @property tags 标签
 * @property isDeliveryException 是否配送异常
 * @property timeslots 时间槽
 * @property cancelAddCartText 取消订单后添加到购物车的文本提示
 */
data class OrderDetailData(
    val status: Int = 0,
    val cashierswitch: Int = 0,
    val activityList: List<Any> = emptyList(),
    val nimbleIsland: Map<String, Any> = emptyMap(),
    val statusmsg: String = "",
    val payendtime: Long = 0,
    val ordertype: Int = 0,
    val serviceaction: String = "",
    val showonlineservice: Int = 0,
    val pricedetail: List<OrderDetailPriceDetail> = emptyList(),
    val orderpayinfo: OrderPayInfo? = null,
    val baseinfo: BaseInfo? = null,
    val shopinfo: OrderDetailShopInfo? = null,
    val productsinfo: ProductsInfo? = null,
    val commentinfo: CommentInfo? = null,
    val statusinfo: StatusInfo? = null,
    val showmap: Int = 0,
    val servicecanrate: Int = 0,
    val servicecanratetime: Int = 0,
    val canupdatedelivery: Int = 0,
    val ispresale: Int = 0,
    val resourceLocation: List<ResourceLocation> = emptyList(),
    val noTouchPics: NoTouchPics? = null,
    val orderDetailAfterSale: OrderDetailAfterSale? = null,
    val addressTop: Boolean = false,
    val orderMsgOpen: Boolean = false,
    val tags: List<String> = emptyList(),
    val isDeliveryException: Int = 0,
    val timeslots: TimeSlots? = null,
    val cancelAddCartText: String = ""
)

/**
 * 价格详情类
 * @property title 标题
 * @property amount 金额
 * @property highlight 高亮
 * @property descinfo 描述信息
 * @property linyHighlight 林氏高亮
 * @property linyAmount 林氏金额
 */
data class OrderDetailPriceDetail(
    val title: String = "",
    val amount: String = "",
    val highlight: Int = 0,
    val descinfo: OrderDetailDescInfo? = null,
    val linyHighlight: Int? = null,
    val linyAmount: String? = null
)

/**
 * 描述信息类
 * @property title 标题
 * @property desc 描述列表
 */
data class OrderDetailDescInfo(
    val title: String = "",
    val desc: List<OrderDetailDesc> = emptyList()
)

/**
 * 描述类
 * @property content 内容
 * @property type 类型
 */
data class OrderDetailDesc(
    val content: String = "",
    val type: Int = 0
)

/**
 * 订单支付信息类
 * @property totalpayment 总支付
 * @property paydetails 支付详情
 * @property totalpromoamt 总促销金额
 * @property totaldiscount 总折扣
 * @property activityTextInfo 活动文本信息
 */
data class OrderPayInfo(
    val totalpayment: Int = 0,
    val paydetails: String = "",
    val totalpromoamt: Int = 0,
    val totaldiscount: Int = 0,
    val activityTextInfo: OrderDetailActivityTextInfo? = null
)

/**
 * 活动文本信息类
 * @property activitytext 活动文本
 * @property signs 标记列表
 */
data class OrderDetailActivityTextInfo(
    val activitytext: String = "",
    val signs: List<Any> = emptyList()
)

/**
 * 基本信息类
 * @property comment 评论
 * @property id 订单ID
 * @property generateTime 生成时间
 * @property paytypes 支付类型
 * @property outOfStockMsg 缺货消息
 */
data class BaseInfo(
    val comment: String = "",
    val id: String = "",
    val generateTime: Long = 0,
    val paytypes: String = "",
    val outOfStockMsg: String = ""
)

/**
 * 商店信息类
 * @property seller 卖家信息
 * @property shopphone 商店电话
 * @property shopicon 商店图标
 * @property shopId 商店ID
 */
data class OrderDetailShopInfo(
    val seller: OrderDetailSeller? = null,
    val shopphone: String = "",
    val shopicon: String = "",
    val shopId: String = ""
)

/**
 * 卖家信息类
 * @property id 卖家ID
 * @property title 标题
 * @property icon 图标
 * @property action 动作
 */
data class OrderDetailSeller(
    val id: String = "",
    val title: String = "",
    val icon: String = "",
    val action: String = ""
)

/**
 * 商品信息类
 * @property type 类型
 * @property products 商品列表
 */
data class ProductsInfo(
    val type: Int = 0,
    val products: List<OrderDetailProduct> = emptyList()
)

/**
 * 商品类
 * @property id 商品ID
 * @property batchcode 批次代码，等同于originalskucode
 * @property title 标题
 * @property action 动作
 * @property price 价格
 * @property pattern 模式
 * @property spec 规格
 * @property lackOrExchangeNotShow 是否显示缺货或换货
 * @property isbulkitem 是否散装商品
 * @property goodstagid 商品标签ID
 * @property unit 单位
 * @property qty 数量
 * @property barcode 条形码
 * @property actualPaidPrice 实际支付价格
 * @property performanceHourHour 是否小时达
 * @property imgurl 图片URL
 * @property subtitle 副标题
 * @property num 数量
 * @property originalselectstate 原始选择状态
 * @property sellerid 卖家ID
 * @property titletag 标题标签
 * @property calnum 计算数量
 * @property isrefund 是否退款
 * @property bundlepromocode 捆绑促销代码
 * @property skusaletype 销售类型
 * @property afterSaleBtn 售后按钮
 * @property isperformancehourhour 是否小时达
 * @property canNotBuy 是否不可购买
 * @property orderremark 订单备注
 * @property batchdescription 批次描述
 */
data class OrderDetailProduct(
    val id: String = "",
    val batchcode: String = "",
    val title: String = "",
    val action: String = "",
    val price: OrderDetailPrice? = null,
    val pattern: String = "",
    val spec: OrderDetailSpec? = null,
    val lackOrExchangeNotShow: Boolean = false,
    val isbulkitem: Int = 0,
    val goodstagid: Int = 0,
    val unit: String = "",
    val qty: Int = 0,
    val barcode: String = "",
    val actualPaidPrice: Int = 0,
    val performanceHourHour: Boolean = false,
    val imgurl: String = "",
    val subtitle: String = "",
    val num: Int = 0,
    val originalselectstate: Boolean = false,
    val sellerid: String = "",
    val titletag: TitleTag? = null,
    val calnum: String = "",
    val isrefund: Int = 0,
    val bundlepromocode: String = "",
    val skusaletype: Int = 0,
    val afterSaleBtn: AfterSaleBtn? = null,
    val isperformancehourhour: Boolean = false,
    val canNotBuy: Boolean = false,
    val orderremark: String = "",
    val batchdescription: String = ""
)

/**
 * 标题标签类
 * @property type 类型
 * @property text 文本
 * @property sort 排序
 */
data class TitleTag(
    val type: String = "",
    val text: String = "",
    val sort: Int = 0
)

/**
 * 价格类
 * @property total 总价
 * @property value 价值
 * @property market 市场价格
 * @property lineprice 划线价格
 */
data class OrderDetailPrice(
    val total: Int = 0,
    val value: Int = 0,
    val market: Int = 0,
    val lineprice: Int = 0
)

/**
 * 规格类
 * @property desc 描述
 */
data class OrderDetailSpec(
    val desc: String = ""
)

/**
 * 售后按钮类
 * @property highlight 高亮
 * @property dialogMsg 对话框消息
 * @property actionname 动作名称
 * @property actionurl 动作URL
 */
data class AfterSaleBtn(
    val highlight: Int = 0,
    val dialogMsg: String = "",
    val actionname: String = "",
    val actionurl: String? = null
)

/**
 * 评论信息类
 * @property invoicedetail 发票详情
 * @property recvinfo 收货信息
 * @property carriername 承运人姓名
 * @property carrierphone 承运人电话
 * @property riderUrl 骑手URL
 * @property riderLevel 骑手等级
 * @property carrierHealth 承运人健康
 * @property carrierColor 承运人颜色
 * @property carrierGoldUrl 承运人金牌URL
 * @property expecttime 预计时间
 * @property contactInfo 联系信息
 */
data class CommentInfo(
    val invoicedetail: InvoiceDetail? = null,
    val recvinfo: OrderDetailRecvInfo? = null,
    val carriername: String = "",
    val carrierphone: String = "",
    val riderUrl: String = "",
    val riderLevel: String = "",
    val carrierHealth: String = "",
    val carrierColor: String = "",
    val carrierGoldUrl: String = "",
    val expecttime: String = "",
    val contactInfo: ContactInfo? = null
)

/**
 * 发票详情类
 * @property payertype 付款人类型
 * @property amt 金额
 * @property showflag 显示标志
 * @property invoiceflag 发票标志
 * @property patchstatus 补丁状态
 * @property patchstatusdesc 补丁状态描述
 */
data class InvoiceDetail(
    val payertype: Int = 0,
    val amt: Int = 0,
    val showflag: Int = 0,
    val invoiceflag: Int = 0,
    val patchstatus: Int = 0,
    val patchstatusdesc: String = ""
)

/**
 * 收货信息类
 * @property id 收货ID
 * @property name 姓名
 * @property gender 性别
 * @property phone 电话
 * @property address 地址
 * @property location 位置
 */
data class OrderDetailRecvInfo(
    val id: String = "",
    val name: String = "",
    val gender: String = "",
    val phone: String = "",
    val address: OrderDetailAddress? = null,
    val location: OrderDetailLocation? = null
)

/**
 * 地址类
 * @property area 区域
 * @property detail 详情
 */
data class OrderDetailAddress(
    val area: String = "",
    val detail: String = ""
)

/**
 * 位置类
 * @property lat 纬度
 * @property lng 经度
 */
data class OrderDetailLocation(
    val lat: String = "",
    val lng: String = ""
)

/**
 * 联系信息类
 * @property type 类型
 * @property virtualPhone 虚拟电话
 * @property extNumber 分机号
 * @property showLittlePhoneIcon 是否显示小电话图标
 * @property userPhone 用户电话
 * @property showTipDialog 是否显示提示对话框
 * @property popupDialogInfo 弹出对话框信息
 */
data class ContactInfo(
    val type: Int = 0,
    val virtualPhone: String = "",
    val extNumber: String = "",
    val showLittlePhoneIcon: Boolean = false,
    val userPhone: String = "",
    val showTipDialog: Boolean = false,
    val popupDialogInfo: PopupDialogInfo? = null
)

/**
 * 弹出对话框信息类
 * @property title 标题
 * @property subTitle 副标题
 */
data class PopupDialogInfo(
    val title: String = "",
    val subTitle: String = ""
)

/**
 * 状态信息类
 * @property title 标题
 * @property buttons 按钮列表
 * @property recvinfo 收货信息
 * @property textinfo 文本信息
 * @property shopphone 商店电话
 */
data class StatusInfo(
    val title: String = "",
    val buttons: List<Button> = emptyList(),
    val recvinfo: OrderDetailRecvInfo? = null,
    val textinfo: TextInfo? = null,
    val shopphone: String = ""
)

/**
 * 按钮类
 * @property reasons 原因列表
 * @property reasonText 原因文本列表
 * @property highlight 高亮
 * @property retentionInfo 保留信息
 * @property actionname 动作名称
 * @property actiontype 动作类型
 * @property actionurl 动作URL
 */
data class Button(
    val reasons: List<String>? = null,
    val reasonText: List<ReasonText>? = null,
    val highlight: Int = 0,
    val retentionInfo: RetentionInfo? = null,
    val actionname: String = "",
    val actiontype: Int = 0,
    val actionurl: String? = null
)

/**
 * 原因文本类
 * @property reason 原因
 * @property activitytext 活动文本
 */
data class ReasonText(
    val reason: String = "",
    val activitytext: String = ""
)

/**
 * 保留信息类
 * @property orderStatus 订单状态
 * @property title 标题
 * @property actions 操作列表
 * @property buttons 按钮列表
 * @property ppromoAmt 促销金额
 */
data class RetentionInfo(
    val orderStatus: String = "",
    val title: String = "",
    val actions: List<RetentionAction> = emptyList(),
    val buttons: List<Any> = emptyList(),
    val ppromoAmt: String = ""
)

/**
 * 保留操作类
 * @property text 文本
 * @property url URL
 * @property type 类型
 * @property icon 图标
 * @property desc 描述
 */
data class RetentionAction(
    val text: String = "",
    val url: String = "",
    val type: String = "",
    val icon: String = "",
    val desc: String = ""
)

/**
 * 文本信息类
 * @property activitytext 活动文本
 */
data class TextInfo(
    val activitytext: String = ""
)

/**
 * 资源位置类
 * @property title 标题
 * @property image 图片
 * @property link 链接
 * @property height 高度
 * @property width 宽度
 * @property position 位置
 * @property bubble 气泡
 */
data class ResourceLocation(
    val title: String = "",
    val image: String = "",
    val link: String = "",
    val height: Int = 0,
    val width: Int = 0,
    val position: Int = 0,
    val bubble: String? = null
)

/**
 * 无接触图片类
 * @property title 标题
 * @property imgUrl 图片URL
 * @property pics 图片列表
 */
data class NoTouchPics(
    val title: String = "",
    val imgUrl: String = "",
    val pics: List<String> = emptyList()
)

/**
 * 订单售后详情类
 * @property title 标题
 * @property url URL
 * @property toAftersaleListPage 是否跳转到售后列表页
 */
data class OrderDetailAfterSale(
    val title: OrderDetailActivityTextInfo? = null,
    val url: String = "",
    val toAftersaleListPage: Boolean = false
)

/**
 * 时间槽类
 * @property show 是否显示
 * @property data 数据列表
 */
data class TimeSlots(
    val show: Boolean = false,
    val data: List<OrderDetailTimeSlot> = emptyList()
)

/**
 * 时间槽数据类
 * @property time 时间
 * @property name 名称
 */
data class OrderDetailTimeSlot(
    val time: String = "",
    val name: String = ""
) 