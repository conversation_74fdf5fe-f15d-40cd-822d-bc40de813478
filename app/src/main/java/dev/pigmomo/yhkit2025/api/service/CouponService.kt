package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 优惠券服务类
 * 提供优惠券相关的API调用方法
 */
class CouponService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "CouponService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取新人优惠券
     * @return 新人优惠券领取结果
     */
    suspend fun getNewPersonPopup(sellerId: String, shopId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "getNewPersonPopup: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建业务参数
                    val businessParams = buildAppBusinessParams()

                    // 构建URL
                    val urlWithParams =
                        buildAppApiUrl(
                            RequestConfig.Path.NEW_PERSON_POPUP_PATH,
                            businessParams,
                            commonParams
                        )

                    // 构建请求体
                    val requestBody = """{"sellerid":"$sellerId","shopid":"$shopId"}"""

                    val needSignStr =
                        urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                            .replace("=", "")
                            .replace("&", "") + requestBody

                    // 生成签名
                    val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                    if (sign.isEmpty()) {
                        Log.e(tag, "getNewPersonPopup: sign empty, service may not be initialized")
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                        .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_APP }
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getNewPersonPopup: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    if (sellerId.isEmpty() || shopId.isEmpty()) {
                        Log.e(tag, "getNewPersonPopup: sellerId or shopId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramBusinessParams()
                    businessParams["sellerid"] = sellerId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()
                    businessParams["distinctId"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.NEW_PERSON_POPUP_PATH,
                        businessParams,
                        commonParams,
                        "activity"
                    )

                    // 构建请求体
                    val requestBody = """{"sellerid":"$sellerId","shopid":"$shopId"}"""

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM.value,
                        requestHelper.getUid()
                    )
                    if (sign.isEmpty()) {
                        Log.e(tag, "getNewPersonPopup: sign empty, service may not be initialized")
                        return@withContext RequestResult.Error(Exception("服务未初始化"))
                    }
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getXyhBizParams()
                        .ifEmpty { RequestConfig.XYH_BIZ_PARAMS_ORIGIN_MINI_PROGRAM }
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getNewPersonPopup: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, false)

                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 查询社群新人券信息
     * @return 社群新人券信息响应结果
     */
    suspend fun getNewUserCouponInfo(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getNewUserCouponInfo: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["channel"] = "512"
                businessParams["platform"] = "wechatminiprogram"
                businessParams["appid"] = "wxc9cf7c95499ee604"
                businessParams["wechatunionid"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.NEW_USER_COUPON_INFO_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"shopId":"$shopId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getNewUserCouponInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getNewUserCouponInfo: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["channel"] = "512"
                businessParams["appid"] = "wxc9cf7c95499ee604"
                businessParams["wechatunionid"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.NEW_USER_COUPON_INFO_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 构建请求体
                val requestBody = """{"shopId":"$shopId"}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getNewUserCouponInfo: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 领取新人社群券
     * @param activityCode 活动代码
     * @return 领取新人社群券响应结果
     */
    suspend fun getNewUserGroupCoupon(
        activityCode: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getNewUserGroupCoupon: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["channel"] = "512"
                businessParams["platform"] = "wechatminiprogram"
                businessParams["appid"] = "wxc9cf7c95499ee604"
                //TODO：不传入已进群 wechatunionid 不能领取
                businessParams["wechatunionid"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId

                // 构建URL
                val urlWithParams = buildAppApiUrl(
                    RequestConfig.Path.NEW_USER_COUPON_RECEIVE_PATH,
                    businessParams,
                    commonParams
                )

                // 构建请求体
                val requestBody = """{"activityCode":"$activityCode","shopId":"$shopId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getNewUserGroupCoupon: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getNewUserGroupCoupon: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["channel"] = "512"
                businessParams["appid"] = "wxc9cf7c95499ee604"
                businessParams["wechatunionid"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.NEW_USER_COUPON_RECEIVE_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 构建请求体
                val requestBody = """{"activityCode":"$activityCode","shopId":"$shopId"}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getNewUserGroupCoupon: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 查询新人券信息
     * @return 新人券信息响应结果
     */
    suspend fun getNewPersonCouponInfo(sellerId: String, shopId: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val cityId = requestHelper.getCityId()
                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "getNewPersonCouponInfo: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildAppApiUrl(
                        RequestConfig.Path.NEW_PERSON_COUPON_INFO_PATH,
                        businessParams,
                        commonParams
                    )

                    // 构建请求体
                    val requestBody =
                        """{"shopId":"$shopId","filterStatus":true,"isHaveMember":false}"""

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            requestBody,
                            SignType.APP_WEB.value
                        )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getNewPersonCouponInfo: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    val cityId = requestHelper.getCityId()
                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "getNewPersonCouponInfo: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.NEW_PERSON_COUPON_INFO_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    // 构建请求体
                    val requestBody =
                        """{"shopId":"$shopId","filterStatus":true,"isHaveMember":false}"""

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "getNewPersonCouponInfo: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }

    /**
     * 获取优惠券列表
     * @param couponType 优惠券类型 coupon.general、redEnvelope.coupon
     * @return 优惠券列表响应结果
     */
    suspend fun getCouponList(
        couponType: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getCouponList: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()
                if (commonParams.isEmpty()) {
                    Log.e(tag, "getCouponList: commonParams empty, config may not be set")
                    return@withContext RequestResult.Error(Exception("config not set"))
                }

                // 构建业务参数
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.COUPON_LIST_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody =
                    """{"category":0,"cityId":"$cityId","couponlatitude":"$couponType","sellerShops":[{"sellerId":"$sellerId","shopId":"$shopId"}]}"""

                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getCouponList: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCouponList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getCouponList: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.COUPON_LIST_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody =
                    """{"category":0,"cityId":"$cityId","couponlatitude":"$couponType","sellerShops":[{"sellerId":"$sellerId","shopId":"$shopId"}]}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getCouponList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 获取指定优惠券 APP端
     * @param couponCode 优惠券代码
     * @return 获取指定优惠券响应结果
     */
    suspend fun kindCoupon(
        couponCode: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val commonParams = requestHelper.getAppCommonParams()
                val businessParams = buildAppBusinessParams()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.KIND_COUPON_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"promotioncode":"$couponCode"}"""

                val needSignStr =
                    urlWithParams.split("?")[1].split("&").sorted().joinToString("&")
                        .replace("=", "")
                        .replace("&", "") + requestBody

                // 生成签名
                val sign = requestHelper.generateSign(needSignStr, "", SignType.APP.value)
                if (sign.isEmpty()) {
                    Log.e(tag, "getKindCoupon: sign empty, service may not be initialized")
                    return@withContext RequestResult.Error(Exception("服务未初始化"))
                }
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getKindCoupon: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, false, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramBusinessParams()
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["distinctId"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.KIND_COUPON_PATH,
                    businessParams,
                    commonParams,
                    "activity"
                )

                // 构建请求体
                val requestBody = """{"promotioncode":"$couponCode"}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM.value,
                    requestHelper.getUid()
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getKindCoupon: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 获取助力券列表
     * @param pageSize 页大小，默认为10
     * @return 助力券列表响应结果
     */
    suspend fun getBoostCouponList(
        pageSize: String = "10"
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getBoostCouponList: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.BOOST_COUPON_LIST_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"current":1,"size":$pageSize}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getBoostCouponList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                if (sellerId.isEmpty() || shopId.isEmpty()) {
                    Log.e(tag, "getBoostCouponList: sellerId or shopId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.BOOST_COUPON_LIST_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 构建请求体
                val requestBody = """{"current":1,"size":$pageSize}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getBoostCouponList: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 助力助力券
     * @param gameCode 游戏代码
     * @return 助力助力券响应结果
     */
    suspend fun boostCoupon(
        gameCode: String
    ): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "boostCoupon: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams["gameCode"] = gameCode
                businessParams["channel"] = "512"
                businessParams["platform"] = "wechatminiprogram"
                businessParams["appid"] = "wxc9cf7c95499ee604"
                businessParams["wechatunionid"] = ""
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["sellerid"] = ""

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.BOOST_COUPON_PATH,
                        businessParams,
                        commonParams
                    )

                // 生成签名
                val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "boostCoupon: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildAppStandardHeaders(xyhBizParams, true, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()
                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "boostCoupon: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["gameCode"] = gameCode
                businessParams["channel"] = "512"
                businessParams["appid"] = "wxc9cf7c95499ee604"
                businessParams["wechatunionid"] = ""
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["sellerid"] = ""
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.BOOST_COUPON_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "boostCoupon: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 发起助力券
     * @param prizeId 奖品ID
     * @return 发起助力券响应结果
     */
    suspend fun getGameCode(prizeId: String): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()

                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getGameCode: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                // 添加额外参数
                businessParams["prizeId"] = prizeId
                businessParams["gameCode"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["cityid"] = cityId

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.GET_GAME_CODE_PATH,
                        businessParams,
                        commonParams
                    )

                // 生成签名
                val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val webXyhBizParams = requestHelper.getWebXyhBizParams()
                if (webXyhBizParams.isEmpty()) {
                    Log.e(tag, "getGameCode: webXyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                // 发送请求
                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()

                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "getGameCode: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["prizeId"] = prizeId
                businessParams["gameCode"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()
                businessParams["cityid"] = cityId

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.GET_GAME_CODE_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "getGameCode: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                // 发送请求
                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 利用获取优惠券检查活动是否能参与
     * @return 活动检查响应结果
     */
    suspend fun couponGrabbing(requestBody: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["proportion"] = "3.5"
                    businessParams["pagesize"] = "6"
                    businessParams["aid"] = "67ab00743dc0c20007aba4ce"
                    businessParams["versionpro"] = "2"
                    businessParams["salesChannel"] = ""
                    businessParams["uid"] = requestHelper.getUid()
                    businessParams["showmultiseller"] = ""
                    businessParams["shopName"] = ""
                    businessParams["isOldEdition"] = "false"
                    businessParams["userid"] = requestHelper.getUid()
                    businessParams["longitude"] = ""
                    businessParams["latitude"] = ""

                    // 构建URL
                    val urlWithParams =
                        buildAppApiUrl(
                            RequestConfig.Path.CMS_ACTIVITY_REST_PATH,
                            businessParams,
                            commonParams
                        )

                    // 构建请求体
                    val requestBody =
                        requestBody

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            requestBody,
                            SignType.APP_WEB.value
                        )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "checkActivity: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()

                    // 添加额外参数
                    businessParams["proportion"] = "3.5"
                    businessParams["pagesize"] = "6"
                    businessParams["aid"] = "67ab00743dc0c20007aba4ce"
                    businessParams["versionpro"] = "2"
                    businessParams["salesChannel"] = ""
                    businessParams["uid"] = requestHelper.getUid()
                    businessParams["showmultiseller"] = ""
                    businessParams["shopName"] = ""
                    businessParams["isOldEdition"] = "false"
                    businessParams["userid"] = requestHelper.getUid()
                    businessParams["longitude"] = ""
                    businessParams["latitude"] = ""
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams =
                        buildMiniApiUrl(
                            RequestConfig.Path.CMS_ACTIVITY_REST_PATH,
                            businessParams,
                            commonParams,
                            "api"
                        )

                    // 构建请求体
                    val requestBody =
                        requestBody

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "checkActivity: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }

                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }
}