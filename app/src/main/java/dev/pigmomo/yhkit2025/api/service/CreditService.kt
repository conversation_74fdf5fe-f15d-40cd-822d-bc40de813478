package dev.pigmomo.yhkit2025.api.service

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestConfig
import dev.pigmomo.yhkit2025.api.RequestHelper
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.SignType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 积分服务类
 * 提供积分相关的API调用方法
 */
class CreditService(requestHelper: RequestHelper) : BaseService(requestHelper) {

    private val tag = "CreditService"

    private val serviceType = requestHelper.getServiceType()

    /**
     * 获取积分明细
     * @param page 页码，默认从0开始
     * @return 积分明细响应结果
     */
    suspend fun creditDetail(page: Int = 0): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建业务参数
                val businessParams = buildAppWebBusinessParams()
                businessParams.put("page", page.toString())
                businessParams.put("memberid", requestHelper.getUid())

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.CREDIT_DETAILS_PATH,
                        businessParams,
                        commonParams
                    )

                val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val webXyhBizParams = requestHelper.getWebXyhBizParams()
                if (webXyhBizParams.isEmpty()) {
                    Log.e(tag, "creditDetail: webXyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                businessParams["page"] = page.toString()
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.CREDIT_DETAILS_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "creditDetail: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 获取签到奖励详情
     * @return 签到奖励详情响应结果
     */
    suspend fun signRewardDetail(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()

                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "signRewardDetail: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()
                // 添加额外参数
                businessParams["memberId"] = requestHelper.getUid()
                businessParams["shopId"] = shopId
                businessParams["currentLng"] = ""
                businessParams["currentLat"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.SIGN_REWARD_DETAIL_PATH,
                        businessParams,
                        commonParams
                    )

                // 生成签名
                val sign = requestHelper.generateSign(urlWithParams, "", SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val webXyhBizParams = requestHelper.getWebXyhBizParams()
                if (webXyhBizParams.isEmpty()) {
                    Log.e(tag, "signRewardDetail: webXyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                // 发送请求
                requestHelper.get(fullUrl, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()

                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "signRewardDetail: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                // 添加额外参数
                businessParams["memberId"] = requestHelper.getUid()
                businessParams["shopId"] = shopId
                businessParams["currentLng"] = ""
                businessParams["currentLat"] = ""
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.SIGN_REWARD_DETAIL_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    "",
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "signRewardDetail: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                // 发送请求
                requestHelper.get(fullUrl, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 发起积分组队
     * @return 积分组队详情
     */
    suspend fun pointTeam(): RequestResult<String> = withContext(Dispatchers.IO) {
        when (serviceType) {
            "app" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()

                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "pointTeam: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getAppCommonParams()

                // 构建Web业务参数
                val businessParams = buildAppWebBusinessParams()

                // 添加额外参数
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams =
                    buildAppApiUrl(
                        RequestConfig.Path.TEAM_DETAIL_PATH,
                        businessParams,
                        commonParams
                    )

                // 构建请求体
                val requestBody = """{"shopId":"$shopId"}"""

                // 生成签名
                val sign =
                    requestHelper.generateSign(urlWithParams, requestBody, SignType.APP_WEB.value)
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val webXyhBizParams = requestHelper.getWebXyhBizParams()
                if (webXyhBizParams.isEmpty()) {
                    Log.e(tag, "pointTeam: webXyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                }

                val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            "mini" -> {
                val sellerId = requestHelper.getSellerId()
                val shopId = requestHelper.getShopId()
                val cityId = requestHelper.getCityId()

                if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                    Log.e(tag, "pointTeam: sellerId or shopId or cityId is empty")
                    return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                }

                // 构建通用参数
                val commonParams = requestHelper.getMiniProgramCommonParams()

                // 构建业务参数
                val businessParams = buildMiniProgramWebBusinessParams()
                // 添加额外参数
                businessParams["sellerid"] = sellerId
                businessParams["cityid"] = cityId
                businessParams["shopid"] = shopId
                businessParams["memberid"] = requestHelper.getUid()

                // 构建URL
                val urlWithParams = buildMiniApiUrl(
                    RequestConfig.Path.TEAM_DETAIL_PATH,
                    businessParams,
                    commonParams,
                    "api"
                )

                // 构建请求体
                val requestBody = """{"shopId":"$shopId"}"""

                // 生成签名
                val sign = requestHelper.generateSign(
                    urlWithParams,
                    requestBody,
                    SignType.MINIPROGRAM_WEB_ACTIVITY.value
                )
                val fullUrl = "$urlWithParams&sign=$sign"

                // 构建请求头
                val xyhBizParams = requestHelper.getWebXyhBizParams()
                if (xyhBizParams.isEmpty()) {
                    Log.e(tag, "pointTeam: xyhBizParams empty")
                    return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                }
                val headers = buildMiniStandardHeaders(xyhBizParams, true)

                // 发送请求
                requestHelper.postJson(fullUrl, requestBody, headers)
            }

            else -> {
                return@withContext RequestResult.Error(Exception("未知服务类型"))
            }
        }
    }

    /**
     * 积分组队
     * @param teamCode 团队代码
     * @return 积分组队响应结果
     */
    suspend fun joinPointTeam(teamCode: String): RequestResult<String> =
        withContext(Dispatchers.IO) {
            when (serviceType) {
                "app" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "joinPointTeam: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getAppCommonParams()

                    // 构建Web业务参数
                    val businessParams = buildAppWebBusinessParams()

                    // 添加额外参数
                    businessParams["channel"] = "512"
                    businessParams["platform"] = "wechatminiprogram"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId

                    // 构建URL
                    val urlWithParams =
                        buildAppApiUrl(
                            RequestConfig.Path.JOIN_THE_PARTY_PATH,
                            businessParams,
                            commonParams
                        )

                    // 构建请求体
                    val requestBody = """{"teamCode":"$teamCode","shopId":"$shopId"}"""

                    // 生成签名
                    val sign =
                        requestHelper.generateSign(
                            urlWithParams,
                            requestBody,
                            SignType.APP_WEB.value
                        )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val webXyhBizParams = requestHelper.getWebXyhBizParams()
                    if (webXyhBizParams.isEmpty()) {
                        Log.e(tag, "joinPointTeam: webXyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("webXyhBizParams not set"))
                    }

                    val headers = buildAppStandardHeaders(webXyhBizParams, true, false)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                "mini" -> {
                    val sellerId = requestHelper.getSellerId()
                    val shopId = requestHelper.getShopId()
                    val cityId = requestHelper.getCityId()

                    if (sellerId.isEmpty() || shopId.isEmpty() || cityId.isEmpty()) {
                        Log.e(tag, "joinPointTeam: sellerId or shopId or cityId is empty")
                        return@withContext RequestResult.Error(Exception("sellerId or shopId or cityId is empty"))
                    }

                    // 构建通用参数
                    val commonParams = requestHelper.getMiniProgramCommonParams()

                    // 构建业务参数
                    val businessParams = buildMiniProgramWebBusinessParams()
                    // 添加额外参数
                    businessParams["channel"] = "512"
                    businessParams["appid"] = "wxc9cf7c95499ee604"
                    businessParams["wechatunionid"] = ""
                    businessParams["sellerid"] = sellerId
                    businessParams["cityid"] = cityId
                    businessParams["shopid"] = shopId
                    businessParams["memberid"] = requestHelper.getUid()

                    // 构建URL
                    val urlWithParams = buildMiniApiUrl(
                        RequestConfig.Path.JOIN_THE_PARTY_PATH,
                        businessParams,
                        commonParams,
                        "api"
                    )

                    // 构建请求体
                    val requestBody = """{"teamCode":"$teamCode","shopId":"$shopId"}"""

                    // 生成签名
                    val sign = requestHelper.generateSign(
                        urlWithParams,
                        requestBody,
                        SignType.MINIPROGRAM_WEB_ACTIVITY.value
                    )
                    val fullUrl = "$urlWithParams&sign=$sign"

                    // 构建请求头
                    val xyhBizParams = requestHelper.getWebXyhBizParams()
                    if (xyhBizParams.isEmpty()) {
                        Log.e(tag, "joinPointTeam: xyhBizParams empty")
                        return@withContext RequestResult.Error(Exception("xyhBizParams not set"))
                    }
                    val headers = buildMiniStandardHeaders(xyhBizParams, true)

                    // 发送请求
                    requestHelper.postJson(fullUrl, requestBody, headers)
                }

                else -> {
                    return@withContext RequestResult.Error(Exception("未知服务类型"))
                }
            }
        }
}