# classes2.dex

.class public Lcn/yonghui/hyd/order/detail/OrderDetailActivity;
.super Lcn/yonghui/hyd/lib/activity/BaseYHTitleActivity;

# interfaces
.implements Lko/d;
.implements Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior$b;
.implements Lko/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcn/yonghui/hyd/order/detail/OrderDetailActivity$n;,
        Lcn/yonghui/hyd/order/detail/OrderDetailActivity$m;
    }
.end annotation


# static fields
.field public static changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect; = null

.field private static final u1:I = 0x1

.field private static final v1:Ljava/lang/String; = "myyh://yhlife.com/show/native?name=home"

.field public static w1:I = 0x3ea


# instance fields
.field private A:Landroid/view/View;

.field private B:Landroid/widget/TextView;

.field private C:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

.field private D:Landroid/view/ViewStub;

.field private E:Landroid/view/View;

.field private F:Luo/k;

.field public G:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;",
            ">;"
        }
    .end annotation
.end field

.field private I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

.field private J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

.field private K:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;

.field private L:Ljava/util/Timer;

.field private M:Z

.field private N:I

.field private O:I

.field private P:I

.field private Q:Lcom/baidu/mapapi/map/MapView;

.field private R:Landroid/view/View;

.field private S:Lko/w;

.field private T:Z

.field private U:Z

.field private V:Z

.field private W:Lcn/yonghui/hyd/lib/view/GoPushSettingView;

.field private X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

.field private Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

.field private Z:Landroidx/constraintlayout/widget/ConstraintLayout;

.field private a:Z

.field private b:Lw9/b;

.field private c:Ljava/lang/String;

.field private d:Z

.field private e:Lcn/yonghui/hyd/order/detail/c;

.field private e1:Landroid/widget/LinearLayout;

.field public f:Lro/c;

.field private f1:Landroid/widget/TextView;

.field private g:Landroidx/recyclerview/widget/RecyclerView;

.field private g1:Landroid/widget/LinearLayout;

.field private h:Z

.field private h1:Landroid/widget/LinearLayout;

.field private i:Luo/h;

.field public i1:Z

.field private j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

.field private j1:Lzo/p;

.field private k:Lcn/yonghui/hyd/order/base/RecommendBean;

.field private k1:Landroid/os/Handler;

.field private l:Landroid/widget/RelativeLayout;

.field private l1:Landroid/view/View;

.field private m:Landroid/widget/TextView;

.field private m1:Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;

.field private n:Lcn/yonghui/hyd/coreui/widget/IconFont;

.field public n1:Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

.field private o:Landroid/widget/TextView;

.field public o1:Ljava/util/TimerTask;

.field private p:Lcn/yonghui/hyd/coreui/widget/IconFont;

.field private p1:Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;

.field private q:Lcn/yonghui/hyd/coreui/widget/IconFont;

.field private q1:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;

.field private r:Landroid/widget/TextView;

.field private r1:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;",
            ">;"
        }
    .end annotation
.end field

.field private s:Landroid/view/View;

.field private s1:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;",
            ">;"
        }
    .end annotation
.end field

.field private t:Lcom/airbnb/lottie/LottieAnimationView;

.field public t1:Landroid/view/View;

.field private u:Landroid/widget/TextView;

.field private v:Lcn/yonghui/hyd/coreui/widget/IconFont;

.field private w:Landroid/widget/LinearLayout;

.field private x:Landroid/widget/TextView;

.field private y:Landroid/view/View;

.field private z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;


# direct methods
.method public static constructor <clinit>()V
    .registers 0

    return-void
.end method

.method public constructor <init>()V
    .registers 3

    invoke-direct {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHTitleActivity;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->a:Z

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->d:Z

    const/4 v1, 0x1

    iput-boolean v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->h:Z

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->M:Z

    iput v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->N:I

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->U:Z

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->V:Z

    const/4 v1, 0x0

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->W:Lcn/yonghui/hyd/lib/view/GoPushSettingView;

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i1:Z

    new-instance v0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$m;

    invoke-direct {v0, p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$m;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k1:Landroid/os/Handler;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n1:Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    return-void
.end method

.method private Ac(Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V
    .registers 24

    move-object/from16 v0, p1

    move/from16 v1, p2

    move-object/from16 v2, p3

    const/4 v3, 0x3

    new-array v8, v3, [Ljava/lang/Object;

    const/4 v10, 0x0

    aput-object v0, v8, v10

    invoke-static/range {p2 .. p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/4 v11, 0x1

    aput-object v4, v8, v11

    const/4 v12, 0x2

    aput-object v2, v8, v12

    const-string v5, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v6, "exceptionOrderHandle"

    const-string v7, "(Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V"

    const/4 v9, 0x2

    move-object/from16 v4, p0

    invoke-static/range {v4 .. v9}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v13, v3, [Ljava/lang/Object;

    aput-object v0, v13, v10

    new-instance v4, Ljava/lang/Integer;

    invoke-direct {v4, v1}, Ljava/lang/Integer;-><init>(I)V

    aput-object v4, v13, v11

    aput-object v2, v13, v12

    sget-object v15, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v3, v3, [Ljava/lang/Class;

    const-class v4, Ljava/lang/String;

    aput-object v4, v3, v10

    sget-object v4, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v4, v3, v11

    const-class v4, Lcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;

    aput-object v4, v3, v12

    sget-object v19, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/16 v16, 0x0

    const v17, 0x9f82

    move-object/from16 v14, p0

    move-object/from16 v18, v3

    invoke-static/range {v13 .. v19}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v3

    iget-boolean v3, v3, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v3, :cond_53

    return-void

    :cond_53
    const/4 v3, 0x0

    move-object/from16 v4, p0

    invoke-direct {v4, v3, v0, v1, v2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->zc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V

    return-void
.end method

.method private Bc()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f4e

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object v1

    if-eqz v1, :cond_4c

    const-string v2, "order_id"

    invoke-virtual {v1, v2}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_26

    goto :goto_4c

    :cond_26
    invoke-virtual {v1, v2}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    iput-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const-string v2, "back_to_home"

    invoke-virtual {v1, v2, v0}, Landroid/content/Intent;->getBooleanExtra(Ljava/lang/String;Z)Z

    move-result v0

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->d:Z

    const-string v0, "showRiderGifts"

    invoke-virtual {v1, v0}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_4b

    invoke-virtual {v1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "1"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4b

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->a:Z

    :cond_4b
    return-void

    :cond_4c
    :goto_4c
    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method private Cc()Ljava/lang/String;
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v7, Ljava/lang/String;

    const/4 v4, 0x0

    const v5, 0x9f70

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v2, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v2, :cond_1b

    iget-object v0, v1, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    return-object v0

    :cond_1b
    invoke-direct {p0, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Dc(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private Dc(I)Ljava/lang/String;
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p1}, Ljava/lang/Integer;-><init>(I)V

    const/4 v8, 0x0

    aput-object v2, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v2, v6, v8

    const-class v7, Ljava/lang/String;

    const/4 v4, 0x0

    const v5, 0x9f72

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v2, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v2, :cond_27

    iget-object p1, v1, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast p1, Ljava/lang/String;

    return-object p1

    :cond_27
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q1:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;

    if-eqz v2, :cond_7a

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;->getProducts()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_7a

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q1:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;->getProducts()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-lez v2, :cond_7a

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q1:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;->getProducts()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v3

    :goto_4c
    if-ge v8, v3, :cond_7a

    if-eqz p1, :cond_63

    if-eq p1, v0, :cond_53

    goto :goto_6e

    :cond_53
    invoke-interface {v2, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcn/yonghui/hyd/data/products/ProductsDataBean;

    iget v4, v4, Lcn/yonghui/hyd/data/products/ProductsDataBean;->num:F

    const/high16 v5, 0x42c80000  # 100.0f

    div-float/2addr v4, v5

    float-to-int v4, v4

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto :goto_6e

    :cond_63
    invoke-interface {v2, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcn/yonghui/hyd/data/products/ProductsDataBean;

    iget-object v4, v4, Lcn/yonghui/hyd/data/products/FloorsDataBean;->id:Ljava/lang/String;

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :goto_6e
    add-int/lit8 v4, v3, -0x1

    if-eq v8, v4, :cond_77

    const-string v4, ","

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_77
    add-int/lit8 v8, v8, 0x1

    goto :goto_4c

    :cond_7a
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private Ec()Ljava/lang/String;
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v7, Ljava/lang/String;

    const/4 v4, 0x0

    const v5, 0x9f71

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v1, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_1b

    iget-object v0, v0, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    return-object v0

    :cond_1b
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Dc(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private Fc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "handleweatherOrFestiveatmosphere"

    const-string v3, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;)V"

    const/4 v5, 0x2

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f58

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    invoke-virtual {p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v0

    if-eqz v0, :cond_95

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getNewZipUrl()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_59

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    invoke-virtual {v2, v7}, Landroid/widget/ImageView;->setVisibility(I)V

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    const/4 v3, -0x1

    invoke-virtual {v2, v3}, Lcom/airbnb/lottie/LottieAnimationView;->setRepeatCount(I)V

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    invoke-virtual {v2, v1}, Lcom/airbnb/lottie/LottieAnimationView;->setAnimationFromUrl(Ljava/lang/String;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    sget-object v2, Lko/m;->a:Lko/m;

    invoke-virtual {v1, v2}, Lcom/airbnb/lottie/LottieAnimationView;->setFailureListener(Lyx/k;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    invoke-virtual {v1}, Lcom/airbnb/lottie/LottieAnimationView;->playAnimation()V

    :cond_59
    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getWeatherType()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_95

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getWeatherType()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_95

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    if-nez v1, :cond_78

    const v1, 0x7f091afb

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    :cond_78
    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    invoke-virtual {v1, v7}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getWeatherType()Ljava/lang/String;

    move-result-object v0

    const-string v1, "snow"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_8e

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    const-string v1, "heavySnow"

    goto :goto_92

    :cond_8e
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    const-string v1, "heavyRainy"

    :goto_92
    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;->a(Ljava/lang/String;)V

    :cond_95
    return-void
.end method

.method private Gc()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f45

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    const v0, 0x7f0910b6

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s:Landroid/view/View;

    const v0, 0x7f0910be

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/airbnb/lottie/LottieAnimationView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t:Lcom/airbnb/lottie/LottieAnimationView;

    const v0, 0x7f0910bf

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->u:Landroid/widget/TextView;

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t:Lcom/airbnb/lottie/LottieAnimationView;

    const-string v1, "refresh.json"

    invoke-virtual {v0, v1}, Lcom/airbnb/lottie/LottieAnimationView;->setAnimation(Ljava/lang/String;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t:Lcom/airbnb/lottie/LottieAnimationView;

    const-string v1, "refreshimages/"

    invoke-virtual {v0, v1}, Lcom/airbnb/lottie/LottieAnimationView;->setImageAssetsFolder(Ljava/lang/String;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t:Lcom/airbnb/lottie/LottieAnimationView;

    const/4 v1, -0x1

    invoke-virtual {v0, v1}, Lcom/airbnb/lottie/LottieAnimationView;->setRepeatCount(I)V

    return-void
.end method

.method private static synthetic Hc(Ljava/lang/Throwable;)V
    .registers 1

    return-void
.end method

.method private synthetic Ic(Landroid/view/View;)V
    .registers 10
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    invoke-static {p1}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/view/View;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f99

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    :goto_20
    invoke-static {p1}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p1}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_27
    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->onBackPressed()V

    goto :goto_20
.end method

.method private synthetic Jc(Landroid/view/View;)V
    .registers 11
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    invoke-static {p1}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v8, 0x0

    aput-object p1, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/view/View;

    aput-object v0, v6, v8

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f98

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    :goto_20
    invoke-static {p1}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p1}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_27
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g1:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v8}, Landroid/widget/LinearLayout;->setVisibility(I)V

    goto :goto_20
.end method

.method private synthetic Kc(Landroid/view/View;)V
    .registers 10
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    invoke-static {p1}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/view/View;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f97

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    :goto_20
    invoke-static {p1}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p1}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_27
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g1:Landroid/widget/LinearLayout;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    goto :goto_20
.end method

.method private synthetic Lc(Landroid/view/View;)V
    .registers 10
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    invoke-static {p1}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/view/View;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f96

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    :goto_20
    invoke-static {p1}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p1}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_27
    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->kd()V

    goto :goto_20
.end method

.method private synthetic Mc(Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "lambda$initView$4"

    const-string v3, "(Lcn/yonghui/hyd/data/repository/resource/Resource;)V"

    const/16 v5, 0x1002

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/data/repository/resource/Resource;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f95

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2e

    return-void

    :cond_2e
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    move-result-object v0

    sget-object v1, Lcn/yonghui/hyd/data/repository/resource/a;->a:Lcn/yonghui/hyd/data/repository/resource/a;

    if-ne v0, v1, :cond_55

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_4e

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;->isCanUpdate()Z

    move-result v0

    if-eqz v0, :cond_4e

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    invoke-virtual {v0, p0}, Lcn/yonghui/hyd/order/detail/c;->C(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    goto :goto_69

    :cond_4e
    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->ad(Lcn/yonghui/hyd/data/repository/resource/Resource;)V

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->wc()V

    goto :goto_69

    :cond_55
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    move-result-object v0

    sget-object v1, Lcn/yonghui/hyd/data/repository/resource/a;->b:Lcn/yonghui/hyd/data/repository/resource/a;

    if-ne v0, v1, :cond_64

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->wc()V

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->ad(Lcn/yonghui/hyd/data/repository/resource/Resource;)V

    goto :goto_69

    :cond_64
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    sget-object v0, Lcn/yonghui/hyd/data/repository/resource/a;->c:Lcn/yonghui/hyd/data/repository/resource/a;

    :goto_69
    return-void
.end method

.method private synthetic Nc(Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "lambda$initView$5"

    const-string v3, "(Lcn/yonghui/hyd/data/repository/resource/Resource;)V"

    const/16 v5, 0x1002

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/data/repository/resource/Resource;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f94

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2e

    return-void

    :cond_2e
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    move-result-object v0

    sget-object v1, Lcn/yonghui/hyd/data/repository/resource/a;->a:Lcn/yonghui/hyd/data/repository/resource/a;

    const-string v2, ""

    if-ne v0, v1, :cond_e7

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_84

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getCode()Ljava/lang/Integer;

    move-result-object v0

    if-eqz v0, :cond_84

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getCode()Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    if-nez v0, :cond_84

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_7d

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getData()Lcn/yonghui/hyd/data/repository/model/BaseModel;

    move-result-object v0

    if-eqz v0, :cond_7d

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getData()Lcn/yonghui/hyd/data/repository/model/BaseModel;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckResult;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckResult;->getAction()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    move-result-object v1

    :cond_7d
    if-eqz v1, :cond_11a

    invoke-direct {p0, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->xc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;)V

    goto/16 :goto_11a

    :cond_84
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_c1

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getData()Lcn/yonghui/hyd/data/repository/model/BaseModel;

    move-result-object v0

    if-eqz v0, :cond_c1

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v1}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getData()Lcn/yonghui/hyd/data/repository/model/BaseModel;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckResult;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckResult;->getMsg()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v3}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getData()Lcn/yonghui/hyd/data/repository/model/BaseModel;

    move-result-object v3

    check-cast v3, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckResult;

    invoke-virtual {v3}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckResult;->getPopConfigList()Ljava/util/ArrayList;

    move-result-object v3

    goto :goto_c4

    :cond_c1
    move-object v3, v1

    move-object v0, v2

    move-object v1, v0

    :goto_c4
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v4

    if-nez v4, :cond_d6

    if-eqz v3, :cond_d6

    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v4

    if-lez v4, :cond_d6

    invoke-direct {p0, v1, v2, v3}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->vc(Ljava/lang/String;Ljava/lang/String;Ljava/util/ArrayList;)V

    return-void

    :cond_d6
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_11a

    const v1, 0x7f1207be

    invoke-static {v1}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v0, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->uc(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_11a

    :cond_e7
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    move-result-object v0

    sget-object v1, Lcn/yonghui/hyd/data/repository/resource/a;->b:Lcn/yonghui/hyd/data/repository/resource/a;

    if-ne v0, v1, :cond_115

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_10b

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getMessage()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_10b

    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/data/repository/model/BaseResp;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseResp;->getMessage()Ljava/lang/String;

    move-result-object v2

    :cond_10b
    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_11a

    invoke-static {v2}, Lcn/yonghui/hyd/lib/style/UiUtil;->showToast(Ljava/lang/CharSequence;)V

    goto :goto_11a

    :cond_115
    invoke-virtual {p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    sget-object v0, Lcn/yonghui/hyd/data/repository/resource/a;->c:Lcn/yonghui/hyd/data/repository/resource/a;

    :cond_11a
    :goto_11a
    return-void
.end method

.method private synthetic Oc(Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 17

    const/4 v0, 0x1

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v5, v7

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "lambda$observerLiveData$6"

    const-string v4, "(Lcn/yonghui/hyd/data/repository/resource/Resource;)V"

    const/16 v6, 0x1002

    move-object v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v8, v0, [Ljava/lang/Object;

    aput-object p1, v8, v7

    sget-object v10, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v13, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/data/repository/resource/Resource;

    aput-object v0, v13, v7

    sget-object v14, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v11, 0x0

    const v12, 0x9f93

    move-object v9, p0

    invoke-static/range {v8 .. v14}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2e

    return-void

    :cond_2e
    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getStatus()Lcn/yonghui/hyd/data/repository/resource/a;

    move-result-object v0

    sget-object v1, Lcn/yonghui/hyd/data/repository/resource/a;->a:Lcn/yonghui/hyd/data/repository/resource/a;

    if-ne v0, v1, :cond_48

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/common/network/modelbean/MemberLevelBean;

    invoke-virtual {v0}, Lcn/yonghui/hyd/common/network/modelbean/MemberLevelBean;->getExclusiveService()Z

    move-result v0

    move-object v1, p0

    invoke-virtual {p0, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->R2(Z)V

    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->A5()V

    goto :goto_49

    :cond_48
    move-object v1, p0

    :goto_49
    return-void
.end method

.method private synthetic Pc(Landroid/view/View;)V
    .registers 11
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    invoke-static {p1}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v8, 0x0

    aput-object p1, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/view/View;

    aput-object v0, v6, v8

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f8f

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    :goto_20
    invoke-static {p1}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p1}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_27
    const-string v0, "myyh://yhlife.com/show/native?name=home"

    invoke-static {p0, v0, v8}, Lcn/yonghui/hyd/lib/style/Navigation;->startSchema(Landroid/content/Context;Ljava/lang/String;Z)Z

    goto :goto_20
.end method

.method private synthetic Qc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V
    .registers 12
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    const/4 v6, 0x2

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const/4 v8, 0x1

    aput-object p2, v4, v8

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "lambda$setBottomBtns$15"

    const-string v3, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V"

    const/16 v5, 0x1002

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    invoke-static {p2}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    aput-object p2, v0, v8

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    aput-object v1, v5, v7

    const-class v1, Landroid/view/View;

    aput-object v1, v5, v8

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f8b

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_40

    :goto_39
    invoke-static {p2}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p2}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_40
    invoke-static {}, Lcn/yonghui/hyd/lib/utils/util/TimeUtils;->isFastDoubleClick()Z

    move-result v0

    if-eqz v0, :cond_47

    goto :goto_39

    :cond_47
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder$a;

    iget-object v3, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v6

    iget-object v8, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    move-object v1, p2

    move-object v2, p1

    move-object v4, p0

    move-object v5, p0

    move-object v7, p0

    invoke-virtual/range {v0 .. v8}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder$a;->b(Landroid/view/View;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/content/Context;Lko/b;Landroidx/fragment/app/FragmentManager;Lko/c;Ljava/lang/String;)V

    goto :goto_39
.end method

.method private synthetic Rc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V
    .registers 12
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    const/4 v6, 0x2

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const/4 v8, 0x1

    aput-object p2, v4, v8

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "lambda$setBottomBtns$16"

    const-string v3, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V"

    const/16 v5, 0x1002

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    invoke-static {p2}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    aput-object p2, v0, v8

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    aput-object v1, v5, v7

    const-class v1, Landroid/view/View;

    aput-object v1, v5, v8

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f8a

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_40

    :goto_39
    invoke-static {p2}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p2}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_40
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder$a;

    iget-object v3, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v6

    iget-object v8, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    move-object v1, p2

    move-object v2, p1

    move-object v4, p0

    move-object v5, p0

    move-object v7, p0

    invoke-virtual/range {v0 .. v8}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder$a;->b(Landroid/view/View;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/content/Context;Lko/b;Landroidx/fragment/app/FragmentManager;Lko/c;Ljava/lang/String;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g1:Landroid/widget/LinearLayout;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    goto :goto_39
.end method

.method private synthetic Sc()V
    .registers 11

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f89

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getTag()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_42

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v0}, Landroid/view/View;->getTag()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    if-eqz v0, :cond_42

    sget-object v1, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder$a;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v2}, Landroid/view/View;->getTag()Ljava/lang/Object;

    move-result-object v0

    move-object v3, v0

    check-cast v3, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    iget-object v4, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v7

    iget-object v9, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    move-object v5, p0

    move-object v6, p0

    move-object v8, p0

    invoke-virtual/range {v1 .. v9}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder$a;->b(Landroid/view/View;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/content/Context;Lko/b;Landroidx/fragment/app/FragmentManager;Lko/c;Ljava/lang/String;)V

    :cond_42
    return-void
.end method

.method private synthetic Tc()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f90

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->w(Ljava/lang/String;)V

    invoke-virtual {p0, v2}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->setLoadingContainerVisible(Z)V

    return-void
.end method

.method public static synthetic Ub(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroid/view/View;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Lc(Landroid/view/View;)V

    return-void
.end method

.method private synthetic Uc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/view/View;)V
    .registers 19
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    const/4 v0, 0x2

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v5, v7

    const/4 v8, 0x1

    aput-object p2, v5, v8

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "lambda$setOrderDetailPageData$12"

    const-string v4, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/view/View;)V"

    const/16 v6, 0x1002

    move-object/from16 v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    invoke-static/range {p2 .. p2}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    new-array v9, v0, [Ljava/lang/Object;

    aput-object p1, v9, v7

    aput-object p2, v9, v8

    sget-object v11, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v14, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    aput-object v0, v14, v7

    const-class v0, Landroid/view/View;

    aput-object v0, v14, v8

    sget-object v15, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v12, 0x0

    const v13, 0x9f8e

    move-object/from16 v10, p0

    invoke-static/range {v9 .. v15}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_42

    :goto_3b
    invoke-static/range {p2 .. p2}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static/range {p2 .. p2}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_42
    invoke-direct/range {p0 .. p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->tc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V

    goto :goto_3b
.end method

.method public static synthetic Vb(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/view/View;)V
    .registers 3

    invoke-direct {p0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Uc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Landroid/view/View;)V

    return-void
.end method

.method private synthetic Vc(Ljava/lang/String;Ljava/lang/Integer;)Lsa0/c2;
    .registers 11

    const/4 v0, 0x2

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 v3, 0x1

    aput-object p2, v1, v3

    sget-object p2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    const-class v0, Ljava/lang/Integer;

    aput-object v0, v6, v3

    const-class v7, Lsa0/c2;

    const/4 v4, 0x0

    const v5, 0x9f8d

    move-object v2, p0

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object p2

    iget-boolean v0, p2, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2a

    iget-object p1, p2, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast p1, Lsa0/c2;

    return-object p1

    :cond_2a
    sget-object p2, Lcn/yonghui/hyd/lib/style/qiyu/QiYuUtil;->INSTANCE:Lcn/yonghui/hyd/lib/style/qiyu/QiYuUtil;

    invoke-virtual {p2, p1}, Lcn/yonghui/hyd/lib/style/qiyu/QiYuUtil;->setQuestionTag(Ljava/lang/String;)V

    invoke-virtual {p0}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p2, p1, v0, v0}, Lcn/yonghui/hyd/lib/style/qiyu/QiYuUtil;->startChat(Landroid/content/Context;Landroid/net/Uri;Lcn/yonghui/hyd/lib/style/bean/QiyuProductBean;)V

    return-object v0
.end method

.method public static synthetic Wb(Ljava/lang/Throwable;)V
    .registers 1

    invoke-static {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Hc(Ljava/lang/Throwable;)V

    return-void
.end method

.method private synthetic Wc(Landroid/view/View;)V
    .registers 10
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    invoke-static {p1}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v4, Landroid/view/View;

    aput-object v4, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f92

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_27

    :goto_20
    invoke-static {p1}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static {p1}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_27
    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->V:Z

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->u(Ljava/lang/String;)V

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->od()V

    goto :goto_20
.end method

.method public static synthetic Xb(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V
    .registers 1

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Tc()V

    return-void
.end method

.method private synthetic Xc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;Landroid/view/View;)V
    .registers 19
    .annotation build Lcom/sensorsdata/analytics/android/sdk/SensorsDataInstrumented;
    .end annotation

    .annotation build Lwv/g;
    .end annotation

    const/4 v0, 0x2

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v5, v7

    const/4 v8, 0x1

    aput-object p2, v5, v8

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "lambda$setOrdermapInfo$8"

    const-string v4, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;Landroid/view/View;)V"

    const/16 v6, 0x1002

    move-object/from16 v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    invoke-static/range {p2 .. p2}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    new-array v9, v0, [Ljava/lang/Object;

    aput-object p1, v9, v7

    aput-object p2, v9, v8

    sget-object v11, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v14, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;

    aput-object v0, v14, v7

    const-class v0, Landroid/view/View;

    aput-object v0, v14, v8

    sget-object v15, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v12, 0x0

    const v13, 0x9f91

    move-object/from16 v10, p0

    invoke-static/range {v9 .. v15}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_42

    :goto_3b
    invoke-static/range {p2 .. p2}, Lcom/sensorsdata/analytics/android/autotrack/aop/SensorsDataAutoTrackHelper;->trackViewOnClick(Landroid/view/View;)V

    invoke-static/range {p2 .. p2}, Lwv/e;->p(Landroid/view/View;)V

    return-void

    :cond_42
    invoke-virtual/range {p0 .. p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->getContext()Landroidx/appcompat/app/AppCompatActivity;

    move-result-object v0

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getAction()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1, v7}, Lcn/yonghui/hyd/lib/style/Navigation;->startSchema(Landroid/content/Context;Ljava/lang/String;Z)Z

    goto :goto_3b
.end method

.method public static synthetic Yb(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroid/view/View;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Kc(Landroid/view/View;)V

    return-void
.end method

.method private synthetic Yc(Ljava/lang/String;)Lsa0/c2;
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    const-class v7, Lsa0/c2;

    const/4 v4, 0x0

    const v5, 0x9f8c

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v1, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_22

    iget-object p1, v0, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast p1, Lsa0/c2;

    return-object p1

    :cond_22
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    if-eqz v0, :cond_29

    invoke-virtual {v0, p1}, Lcn/yonghui/hyd/order/detail/c;->a(Ljava/lang/String;)V

    :cond_29
    const/4 p1, 0x0

    return-object p1
.end method

.method public static synthetic Zb(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroid/view/View;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Jc(Landroid/view/View;)V

    return-void
.end method

.method private Zc()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f49

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->b:Lw9/b;

    invoke-virtual {v0}, Lw9/b;->e()Lnf/a;

    move-result-object v0

    new-instance v1, Lko/g;

    invoke-direct {v1, p0}, Lko/g;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, p0, v1}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/j0;)V

    return-void
.end method

.method public static synthetic ac(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V
    .registers 1

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Sc()V

    return-void
.end method

.method private ad(Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcn/yonghui/hyd/data/repository/resource/Resource<",
            "+",
            "Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v5, v7

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "orderCanNotUpdate"

    const-string v4, "(Lcn/yonghui/hyd/data/repository/resource/Resource;)V"

    const/4 v6, 0x2

    move-object v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v8, v0, [Ljava/lang/Object;

    aput-object p1, v8, v7

    sget-object v10, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v13, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/data/repository/resource/Resource;

    aput-object v0, v13, v7

    sget-object v14, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v11, 0x0

    const v12, 0x9f4d

    move-object v9, p0

    invoke-static/range {v8 .. v14}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    if-eqz p1, :cond_50

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_50

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;->getMsg()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_50

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;->getMsg()Ljava/lang/String;

    move-result-object v0

    goto :goto_65

    :cond_50
    if-eqz p1, :cond_63

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_63

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/data/repository/resource/Resource;->getResponse()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/update/model/UpdateOrderInfoResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/data/repository/model/BaseModel;->getErrorMessage()Ljava/lang/String;

    move-result-object v0

    goto :goto_65

    :cond_63
    const-string v0, ""

    :goto_65
    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_6e

    invoke-static {v0}, Lcn/yonghui/hyd/lib/style/UiUtil;->showToast(Ljava/lang/CharSequence;)V

    :cond_6e
    return-void
.end method

.method public static synthetic bc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Nc(Lcn/yonghui/hyd/data/repository/resource/Resource;)V

    return-void
.end method

.method public static synthetic cc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroid/view/View;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Ic(Landroid/view/View;)V

    return-void
.end method

.method public static synthetic dc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V
    .registers 3

    invoke-direct {p0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Qc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V

    return-void
.end method

.method private dd()V
    .registers 21

    move-object/from16 v8, p0

    const/4 v9, 0x0

    new-array v1, v9, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v9, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f80

    move-object/from16 v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1a

    return-void

    :cond_1a
    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f1:Landroid/widget/TextView;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setVisibility(I)V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g1:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v0

    const-wide/16 v2, 0x3e8

    if-eqz v0, :cond_292

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v0

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getButtons()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_292

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v0

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getButtons()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_292

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Z:Landroidx/constraintlayout/widget/ConstraintLayout;

    invoke-virtual {v0, v9}, Landroid/view/ViewGroup;->setVisibility(I)V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v0

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getButtons()Ljava/util/List;

    move-result-object v0

    const/4 v4, 0x0

    :goto_66
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v5

    const/4 v6, 0x1

    if-ge v4, v5, :cond_8a

    invoke-interface {v0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    iget-object v7, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v7}, Landroid/widget/LinearLayout;->getChildCount()I

    move-result v7

    sub-int/2addr v7, v6

    if-le v4, v7, :cond_82

    iget-object v6, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    invoke-interface {v6, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_87

    :cond_82
    iget-object v6, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v6, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :goto_87
    add-int/lit8 v4, v4, 0x1

    goto :goto_66

    :cond_8a
    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-static {v0}, Ljava/util/Collections;->reverse(Ljava/util/List;)V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v0}, Landroid/widget/LinearLayout;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Landroidx/constraintlayout/widget/ConstraintLayout$b;

    const/4 v0, 0x0

    const/4 v5, 0x0

    :goto_9a
    iget-object v7, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v7}, Landroid/widget/LinearLayout;->getChildCount()I

    move-result v7

    if-ge v5, v7, :cond_1ec

    iget-object v7, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v7}, Landroid/widget/LinearLayout;->getChildCount()I

    move-result v7

    sub-int/2addr v7, v6

    sub-int/2addr v7, v5

    iget-object v11, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v11, v7}, Landroid/widget/LinearLayout;->getChildAt(I)Landroid/view/View;

    move-result-object v11

    check-cast v11, Landroidx/constraintlayout/widget/ConstraintLayout;

    invoke-virtual {v11, v9}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v12

    check-cast v12, Lcn/yonghui/hyd/lib/style/widget/SubmitButton;

    invoke-virtual {v11, v6}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v11

    check-cast v11, Landroid/widget/TextView;

    iget-object v13, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v13}, Ljava/util/ArrayList;->size()I

    move-result v13

    if-le v13, v5, :cond_1de

    iget-object v13, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v13, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v13

    if-eqz v13, :cond_1de

    iget-object v13, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v13, v5}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActiontype()I

    move-result v14

    sget-object v15, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->Companion:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;

    invoke-virtual {v15}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->w()I

    move-result v6

    if-ne v14, v6, :cond_131

    iget-object v6, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v6

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getPayTime()J

    move-result-wide v16

    invoke-static {}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getDefault()Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;

    move-result-object v6

    invoke-virtual {v6}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getTimeStamp()J

    move-result-wide v18

    cmp-long v6, v16, v18

    if-lez v6, :cond_131

    iget-object v6, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v6

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getPayTime()J

    move-result-wide v16

    invoke-static {}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getDefault()Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;

    move-result-object v6

    invoke-virtual {v6}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getTimeStamp()J

    move-result-wide v18

    sub-long v16, v16, v18

    move-object v14, v11

    div-long v10, v16, v2

    long-to-int v11, v10

    const/16 v10, 0xe10

    if-le v11, v10, :cond_119

    invoke-static {v8, v11}, Lcn/yonghui/hyd/lib/style/UiUtil;->secondsToOrderSecond(Landroid/content/Context;I)Ljava/lang/String;

    move-result-object v10

    goto :goto_11d

    :cond_119
    invoke-static {v8, v11}, Lcn/yonghui/hyd/lib/style/UiUtil;->secondsCountdownToOrderSecond(Landroid/content/Context;I)Ljava/lang/String;

    move-result-object v10

    :goto_11d
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActionname()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    goto :goto_136

    :cond_131
    move-object v14, v11

    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActionname()Ljava/lang/String;

    move-result-object v6

    :goto_136
    invoke-virtual {v12, v6}, Lcn/yonghui/hyd/lib/style/widget/SubmitButton;->setInnerText(Ljava/lang/CharSequence;)V

    invoke-virtual {v12, v9}, Landroid/widget/FrameLayout;->setVisibility(I)V

    invoke-virtual {v12}, Landroid/widget/FrameLayout;->getParent()Landroid/view/ViewParent;

    move-result-object v6

    check-cast v6, Landroidx/constraintlayout/widget/ConstraintLayout;

    invoke-virtual {v6}, Landroid/view/ViewGroup;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v10

    check-cast v10, Landroid/widget/LinearLayout$LayoutParams;

    invoke-virtual {v6}, Landroid/view/ViewGroup;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    move-result-object v11

    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getBubble()Ljava/lang/String;

    move-result-object v17

    invoke-static/range {v17 .. v17}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v17

    const/high16 v2, 0x41700000  # 15.0f

    const/4 v3, 0x2

    if-nez v17, :cond_17b

    invoke-virtual {v14, v9}, Landroid/widget/TextView;->setVisibility(I)V

    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getBubble()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v14, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    new-instance v0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$l;

    invoke-direct {v0, v8, v6, v10}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$l;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroidx/constraintlayout/widget/ConstraintLayout;Landroid/widget/LinearLayout$LayoutParams;)V

    invoke-virtual {v11, v0}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    invoke-virtual/range {p0 .. p0}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    if-eq v7, v3, :cond_173

    const/high16 v2, 0x40400000  # 3.0f

    :cond_173
    invoke-static {v0, v2}, Lcn/yonghui/hyd/lib/style/UiUtil;->dip2px(Landroid/content/Context;F)I

    move-result v0

    iput v0, v10, Landroid/widget/LinearLayout$LayoutParams;->rightMargin:I

    const/4 v2, 0x1

    goto :goto_19e

    :cond_17b
    invoke-virtual {v14, v1}, Landroid/widget/TextView;->setVisibility(I)V

    new-instance v14, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$a;

    invoke-direct {v14, v8, v6, v12, v10}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$a;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroidx/constraintlayout/widget/ConstraintLayout;Lcn/yonghui/hyd/lib/style/widget/SubmitButton;Landroid/widget/LinearLayout$LayoutParams;)V

    invoke-virtual {v11, v14}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    if-eq v7, v3, :cond_193

    invoke-virtual/range {p0 .. p0}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object v2

    const/high16 v7, 0x40800000  # 4.0f

    invoke-static {v2, v7}, Lcn/yonghui/hyd/lib/style/UiUtil;->dip2px(Landroid/content/Context;F)I

    move-result v2

    goto :goto_19b

    :cond_193
    invoke-virtual/range {p0 .. p0}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object v7

    invoke-static {v7, v2}, Lcn/yonghui/hyd/lib/style/UiUtil;->dip2px(Landroid/content/Context;F)I

    move-result v2

    :goto_19b
    iput v2, v10, Landroid/widget/LinearLayout$LayoutParams;->rightMargin:I

    move v2, v0

    :goto_19e
    invoke-virtual {v6, v10}, Landroid/view/ViewGroup;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    const/4 v0, 0x3

    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getHighlight()I

    move-result v6

    const/4 v7, 0x1

    if-eq v6, v7, :cond_1ae

    if-eq v6, v3, :cond_1ad

    const/4 v3, 0x3

    goto :goto_1ae

    :cond_1ad
    const/4 v3, 0x0

    :cond_1ae
    :goto_1ae
    invoke-virtual {v12, v3}, Lcn/yonghui/hyd/lib/style/widget/SubmitButton;->setButtonStyle(I)V

    new-instance v0, Lko/t;

    invoke-direct {v0, v8, v13}, Lko/t;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;)V

    invoke-virtual {v12, v0}, Landroid/widget/FrameLayout;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :try_start_1b9
    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActiontype()I

    move-result v0

    invoke-virtual {v15}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->u()I

    move-result v3

    if-ne v0, v3, :cond_1c8

    iput-object v12, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v12, v13}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    :cond_1c8
    invoke-virtual {v13}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActiontype()I

    move-result v0

    invoke-virtual {v15}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->t()I

    move-result v3

    if-ne v0, v3, :cond_1dc

    iput-object v12, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v12, v13}, Landroid/view/View;->setTag(Ljava/lang/Object;)V
    :try_end_1d7
    .catch Ljava/lang/Exception; {:try_start_1b9 .. :try_end_1d7} :catch_1d8

    goto :goto_1dc

    :catch_1d8
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :cond_1dc
    :goto_1dc
    move v0, v2

    goto :goto_1e5

    :cond_1de
    move-object v14, v11

    invoke-virtual {v12, v1}, Landroid/widget/FrameLayout;->setVisibility(I)V

    invoke-virtual {v14, v1}, Landroid/widget/TextView;->setVisibility(I)V

    :goto_1e5
    add-int/lit8 v5, v5, 0x1

    const-wide/16 v2, 0x3e8

    const/4 v6, 0x1

    goto/16 :goto_9a

    :cond_1ec
    if-eqz v0, :cond_1f5

    invoke-virtual/range {p0 .. p0}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    const/high16 v2, 0x40800000  # 4.0f

    goto :goto_1fb

    :cond_1f5
    invoke-virtual/range {p0 .. p0}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    const/high16 v2, 0x41800000  # 16.0f

    :goto_1fb
    invoke-static {v0, v2}, Lcn/yonghui/hyd/lib/style/UiUtil;->dip2px(Landroid/content/Context;F)I

    move-result v0

    iput v0, v4, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_297

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f1:Landroid/widget/TextView;

    invoke-virtual {v0, v9}, Landroid/widget/TextView;->setVisibility(I)V

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->h1:Landroid/widget/LinearLayout;

    invoke-virtual {v0}, Landroid/widget/LinearLayout;->removeAllViews()V

    const/4 v2, 0x0

    :goto_219
    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge v2, v0, :cond_297

    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    invoke-static/range {p0 .. p0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v3

    const v4, 0x7f0c060e

    const/4 v5, 0x0

    invoke-virtual {v3, v4, v5}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    move-result-object v3

    const v4, 0x7f090dbf

    invoke-virtual {v3, v4}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v4

    check-cast v4, Landroid/widget/TextView;

    const v5, 0x7f090dbe

    invoke-virtual {v3, v5}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v5

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActionname()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v6, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r1:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    const/4 v7, 0x1

    sub-int/2addr v6, v7

    if-ne v2, v6, :cond_259

    const/16 v6, 0x8

    goto :goto_25a

    :cond_259
    const/4 v6, 0x0

    :goto_25a
    invoke-virtual {v5, v6}, Landroid/view/View;->setVisibility(I)V

    new-instance v5, Lko/u;

    invoke-direct {v5, v8, v0}, Lko/u;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;)V

    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :try_start_265
    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActiontype()I

    move-result v5

    sget-object v6, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->Companion:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->u()I

    move-result v10

    if-ne v5, v10, :cond_276

    iput-object v4, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v4, v0}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    :cond_276
    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActiontype()I

    move-result v5

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->t()I

    move-result v6

    if-ne v5, v6, :cond_28a

    iput-object v4, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    invoke-virtual {v4, v0}, Landroid/view/View;->setTag(Ljava/lang/Object;)V
    :try_end_285
    .catch Ljava/lang/Exception; {:try_start_265 .. :try_end_285} :catch_286

    goto :goto_28a

    :catch_286
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :cond_28a
    :goto_28a
    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->h1:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_219

    :cond_292
    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Z:Landroidx/constraintlayout/widget/ConstraintLayout;

    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->setVisibility(I)V

    :cond_297
    iget-object v0, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t1:Landroid/view/View;

    if-eqz v0, :cond_2ab

    iget-boolean v1, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->a:Z

    if-eqz v1, :cond_2ab

    iput-boolean v9, v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->a:Z

    new-instance v1, Lko/j;

    invoke-direct {v1, v8}, Lko/j;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    const-wide/16 v2, 0x3e8

    invoke-virtual {v0, v1, v2, v3}, Landroid/view/View;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_2ab
    return-void
.end method

.method public static synthetic ec(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Ljava/lang/String;)Lsa0/c2;
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Yc(Ljava/lang/String;)Lsa0/c2;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic fc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Oc(Lcn/yonghui/hyd/data/repository/resource/Resource;)V

    return-void
.end method

.method private fd(Z)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f47

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_25

    return-void

    :cond_25
    if-eqz p1, :cond_2d

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t:Lcom/airbnb/lottie/LottieAnimationView;

    invoke-virtual {p1}, Lcom/airbnb/lottie/LottieAnimationView;->playAnimation()V

    goto :goto_32

    :cond_2d
    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->t:Lcom/airbnb/lottie/LottieAnimationView;

    invoke-virtual {p1}, Lcom/airbnb/lottie/LottieAnimationView;->cancelAnimation()V

    :goto_32
    return-void
.end method

.method public static synthetic gc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;Landroid/view/View;)V
    .registers 3

    invoke-direct {p0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Xc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;Landroid/view/View;)V

    return-void
.end method

.method private gd(I)V
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p1}, Ljava/lang/Integer;-><init>(I)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v2, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v5, 0x0

    const v8, 0x9f48

    move-object v2, p0

    move-object v3, v4

    move v4, v5

    move v5, v8

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_26

    return-void

    :cond_26
    if-nez p1, :cond_31

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->u:Landroid/widget/TextView;

    const v0, 0x7f120f20

    :goto_2d
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(I)V

    goto :goto_42

    :cond_31
    if-ne p1, v0, :cond_39

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->u:Landroid/widget/TextView;

    const v0, 0x7f120fcf

    goto :goto_2d

    :cond_39
    const/4 v0, 0x2

    if-ne p1, v0, :cond_42

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->u:Landroid/widget/TextView;

    const v0, 0x7f120fc8

    goto :goto_2d

    :cond_42
    :goto_42
    return-void
.end method

.method public static synthetic hc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Ljava/lang/String;Ljava/lang/Integer;)Lsa0/c2;
    .registers 3

    invoke-direct {p0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Vc(Ljava/lang/String;Ljava/lang/Integer;)Lsa0/c2;

    move-result-object p0

    return-object p0
.end method

.method private hd(I)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p1}, Ljava/lang/Integer;-><init>(I)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f46

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_25

    return-void

    :cond_25
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s:Landroid/view/View;

    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public static synthetic ic(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroid/view/View;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Wc(Landroid/view/View;)V

    return-void
.end method

.method private id()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f85

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->m:Landroid/widget/TextView;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusmsg()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->jd()Z

    move-result v2

    if-eqz v2, :cond_2b

    goto :goto_2d

    :cond_2b
    const/16 v0, 0x8

    :goto_2d
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setVisibility(I)V

    return-void
.end method

.method public static synthetic jc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/data/repository/resource/Resource;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Mc(Lcn/yonghui/hyd/data/repository/resource/Resource;)V

    return-void
.end method

.method private jd()Z
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f84

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v2, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v2, :cond_1f

    iget-object v0, v1, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    return v0

    :cond_1f
    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    if-eqz v1, :cond_56

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getTimeslots()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;

    move-result-object v1

    if-eqz v1, :cond_56

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getTimeslots()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;

    move-result-object v1

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;->getShow()Ljava/lang/Boolean;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    if-eqz v1, :cond_56

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getTimeslots()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;

    move-result-object v1

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;->getData()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_56

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getTimeslots()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;

    move-result-object v1

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;->getData()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_56

    const/4 v0, 0x1

    :cond_56
    return v0
.end method

.method public static synthetic kc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V
    .registers 3

    invoke-direct {p0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Rc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;Landroid/view/View;)V

    return-void
.end method

.method private kd()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f86

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-virtual {v0}, Landroid/widget/TextView;->getVisibility()I

    move-result v0

    if-nez v0, :cond_54

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->l:Landroid/widget/RelativeLayout;

    invoke-virtual {v0}, Landroid/widget/RelativeLayout;->getAlpha()F

    move-result v0

    float-to-double v0, v0

    const-wide v2, 0x3fc999999999999aL  # 0.2

    cmpl-double v4, v0, v2

    if-lez v4, :cond_54

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->m1:Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;

    if-nez v0, :cond_3a

    new-instance v0, Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;

    invoke-direct {v0}, Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;-><init>()V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->m1:Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;

    :cond_3a
    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->jd()Z

    move-result v0

    if-nez v0, :cond_41

    return-void

    :cond_41
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->m1:Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getTimeslots()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;

    move-result-object v2

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderTimeslots;->getData()Ljava/util/List;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lcn/yonghui/hyd/order/detail/dialog/OrderProgressDialog;->Q8(Landroidx/fragment/app/FragmentManager;Ljava/util/List;)V

    :cond_54
    return-void
.end method

.method public static synthetic lc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Landroid/view/View;)V
    .registers 2

    invoke-direct {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Pc(Landroid/view/View;)V

    return-void
.end method

.method public static ld(Landroid/content/Context;Ljava/lang/String;)V
    .registers 10

    const/4 v0, 0x2

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p0, v1, v2

    const/4 v3, 0x1

    aput-object p1, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/content/Context;

    aput-object v0, v6, v2

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v2, 0x0

    const/4 v0, 0x1

    const v5, 0x9f87

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    return-void

    :cond_27
    new-instance v0, Landroid/content/Intent;

    const-string v1, "android.intent.action.VIEW"

    invoke-direct {v0, v1}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    const-class v1, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;

    invoke-virtual {v0, p0, v1}, Landroid/content/Intent;->setClass(Landroid/content/Context;Ljava/lang/Class;)Landroid/content/Intent;

    const-string v1, "order_id"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    invoke-virtual {p0, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    return-void
.end method

.method public static synthetic mc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;
    .registers 1

    iget-object p0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    return-object p0
.end method

.method public static md(Landroid/content/Context;Ljava/lang/String;I)V
    .registers 11

    const/4 v0, 0x3

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p0, v1, v2

    const/4 v3, 0x1

    aput-object p1, v1, v3

    new-instance v4, Ljava/lang/Integer;

    invoke-direct {v4, p2}, Ljava/lang/Integer;-><init>(I)V

    const/4 v5, 0x2

    aput-object v4, v1, v5

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/content/Context;

    aput-object v0, v6, v2

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v3

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v5

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v2, 0x0

    const/4 v0, 0x1

    const v5, 0x9f88

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_33

    return-void

    :cond_33
    new-instance v0, Landroid/content/Intent;

    const-string v1, "android.intent.action.VIEW"

    invoke-direct {v0, v1}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    const-class v1, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;

    invoke-virtual {v0, p0, v1}, Landroid/content/Intent;->setClass(Landroid/content/Context;Ljava/lang/Class;)Landroid/content/Intent;

    const-string v1, "order_id"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    check-cast p0, Landroid/app/Activity;

    invoke-virtual {p0, v0, p2}, Landroid/app/Activity;->startActivityForResult(Landroid/content/Intent;I)V

    return-void
.end method

.method public static synthetic nc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)Lko/w;
    .registers 1

    iget-object p0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->S:Lko/w;

    return-object p0
.end method

.method public static synthetic oc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)Ljava/lang/String;
    .registers 1

    iget-object p0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    return-object p0
.end method

.method private od()V
    .registers 4
    .annotation build Lcn/yonghui/hyd/appframe/statistics/BuryPoint;
    .end annotation

    const-string v0, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v1, "trackClickRefreshMap"

    const/4 v2, 0x0

    invoke-static {p0, v0, v1, v2}, Lcn/yonghui/hyd/appframe/statistics/StatisticsManager;->onStatisticsEvent(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic pc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)Lcn/yonghui/hyd/order/detail/c;
    .registers 1

    iget-object p0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    return-object p0
.end method

.method public static synthetic qc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)Lcn/yonghui/hyd/order/base/RecommendBean;
    .registers 1

    iget-object p0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k:Lcn/yonghui/hyd/order/base/RecommendBean;

    return-object p0
.end method

.method public static synthetic rc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)Landroidx/recyclerview/widget/RecyclerView;
    .registers 1

    iget-object p0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    return-object p0
.end method

.method public static synthetic sc(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V
    .registers 12

    const/4 v0, 0x4

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p0, v1, v2

    const/4 v3, 0x1

    aput-object p1, v1, v3

    new-instance v4, Ljava/lang/Integer;

    invoke-direct {v4, p2}, Ljava/lang/Integer;-><init>(I)V

    const/4 v5, 0x2

    aput-object v4, v1, v5

    const/4 v4, 0x3

    aput-object p3, v1, v4

    sget-object v6, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v0, v0, [Ljava/lang/Class;

    const-class v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;

    aput-object v7, v0, v2

    const-class v2, Ljava/lang/String;

    aput-object v2, v0, v3

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v2, v0, v5

    const-class v2, Lcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;

    aput-object v2, v0, v4

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v2, 0x0

    const/4 v4, 0x1

    const v5, 0x9f9a

    move-object v3, v6

    move-object v6, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_3a

    return-void

    :cond_3a
    invoke-direct {p0, p1, p2, p3}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Ac(Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V

    return-void
.end method

.method private tc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V
    .registers 12
    .annotation build Lcn/yonghui/hyd/appframe/statistics/BuryPoint;
    .end annotation

    const-string v0, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v1, "callService"

    const/4 v2, 0x0

    invoke-static {p0, v0, v1, v2}, Lcn/yonghui/hyd/appframe/statistics/StatisticsManager;->onStatisticsEvent(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x1

    new-array v7, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p1, v7, v1

    const-string v4, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v5, "callService"

    const-string v6, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V"

    const/4 v8, 0x2

    move-object v3, p0

    invoke-static/range {v3 .. v8}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v3, v0, [Ljava/lang/Object;

    aput-object p1, v3, v1

    sget-object v5, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v8, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    aput-object v0, v8, v1

    sget-object v9, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v6, 0x0

    const v7, 0x9f60

    move-object v4, p0

    invoke-static/range {v3 .. v9}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_35

    return-void

    :cond_35
    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->getContext()Landroidx/appcompat/app/AppCompatActivity;

    move-result-object v0

    invoke-virtual {p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getServiceaction()Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2, v1}, Lcn/yonghui/hyd/lib/style/Navigation;->startSchema(Landroid/content/Context;Ljava/lang/String;Z)Z

    return-void
.end method

.method private uc(Ljava/lang/String;Ljava/lang/String;)V
    .registers 13

    const-class v0, Ljava/lang/String;

    const/4 v1, 0x2

    new-array v2, v1, [Ljava/lang/Object;

    const/4 v9, 0x0

    aput-object p1, v2, v9

    const/4 v3, 0x1

    aput-object p2, v2, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v7, v1, [Ljava/lang/Class;

    aput-object v0, v7, v9

    aput-object v0, v7, v3

    sget-object v8, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v5, 0x0

    const v6, 0x9f4b

    move-object v3, p0

    invoke-static/range {v2 .. v8}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_23

    return-void

    :cond_23
    new-instance v0, Ly8/b$a;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-direct {v0, v1}, Ly8/b$a;-><init>(Landroidx/fragment/app/FragmentManager;)V

    invoke-virtual {v0, p2}, Ly8/b$a;->w(Ljava/lang/String;)Ly8/b$a;

    move-result-object p2

    invoke-virtual {p2, p1}, Ly8/b$a;->K(Ljava/lang/String;)Ly8/b$a;

    move-result-object p1

    invoke-virtual {p1, v9}, Ly8/b$a;->C(Z)V

    invoke-virtual {v0}, Ly8/b$a;->a()Ly8/b;

    move-result-object p1

    invoke-virtual {p1}, Ly8/b;->e()V

    return-void
.end method

.method private vc(Ljava/lang/String;Ljava/lang/String;Ljava/util/ArrayList;)V
    .registers 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/util/ArrayList<",
            "Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;",
            ">;)V"
        }
    .end annotation

    const-class v0, Ljava/lang/String;

    const/4 v1, 0x3

    new-array v2, v1, [Ljava/lang/Object;

    const/4 v9, 0x0

    aput-object p1, v2, v9

    const/4 v10, 0x1

    aput-object p2, v2, v10

    const/4 v11, 0x2

    aput-object p3, v2, v11

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v7, v1, [Ljava/lang/Class;

    aput-object v0, v7, v9

    aput-object v0, v7, v10

    const-class v0, Ljava/util/ArrayList;

    aput-object v0, v7, v11

    sget-object v8, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v5, 0x0

    const v6, 0x9f4c

    move-object v3, p0

    invoke-static/range {v2 .. v8}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2a

    return-void

    :cond_2a
    const v0, 0x7f1207be

    if-eqz p3, :cond_83

    invoke-virtual {p3}, Ljava/util/ArrayList;->size()I

    move-result v1

    if-ge v1, v10, :cond_36

    goto :goto_83

    :cond_36
    invoke-virtual {p3, v9}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;->getButton()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p3, v9}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;->getUrl()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p3}, Ljava/util/ArrayList;->size()I

    move-result v2

    if-ge v2, v11, :cond_5e

    invoke-static {v6}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_5a

    invoke-static {v0}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v6

    :cond_5a
    invoke-direct {p0, p1, v6}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->uc(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :cond_5e
    invoke-virtual {p3, v10}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;->getButton()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {p3, v10}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;

    invoke-virtual {p3}, Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;->getUrl()Ljava/lang/String;

    move-result-object p3

    sget-object v2, Ly8/c;->a:Ly8/c;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v3

    new-instance v8, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$e;

    invoke-direct {v8, p0, v1, p3}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$e;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Ljava/lang/String;Ljava/lang/String;)V

    move-object v4, p1

    move-object v5, p2

    invoke-virtual/range {v2 .. v8}, Ly8/c;->e(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcn/yonghui/hyd/common/dialog/FeedBackDialogFragment$a;)V

    return-void

    :cond_83
    :goto_83
    invoke-static {v0}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->uc(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private wc()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f4a

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n1:Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

    if-eqz v0, :cond_1e

    invoke-virtual {v0}, Landroidx/fragment/app/DialogFragment;->dismissAllowingStateLoss()V

    :cond_1e
    return-void
.end method

.method private xc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "cancelOrder"

    const-string v3, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;)V"

    const/4 v5, 0x2

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f44

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    invoke-static {p0}, Lcn/yonghui/hyd/lib/utils/http/legacy/NetWorkUtil;->isNetWorkActive(Landroid/content/Context;)Z

    move-result v0

    if-eqz v0, :cond_5c

    invoke-virtual {p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActionurl()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_66

    invoke-virtual {p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActionurl()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v0

    const-string v1, "url"

    invoke-virtual {v0, v1}, Landroid/net/Uri;->getQueryParameter(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    invoke-interface {v2, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "cn.yonghui.hyd.web.CommonHybridActivity"

    invoke-static {p0, v0, v2}, Lcn/yonghui/hyd/lib/utils/plugin/YHRouter;->navigation(Landroid/content/Context;Ljava/lang/String;Ljava/util/Map;)V

    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->ya()V

    goto :goto_66

    :cond_5c
    const v0, 0x7f120b07

    invoke-virtual {p0, v0}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcn/yonghui/hyd/lib/style/UiUtil;->showToast(Ljava/lang/CharSequence;)V

    :cond_66
    :goto_66
    return-void
.end method

.method private yc(Ljava/lang/String;)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f81

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1e

    return-void

    :cond_1e
    new-instance v0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$b;

    invoke-direct {v0, p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$b;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Ljava/lang/String;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    invoke-virtual {v1, p1, v0}, Lro/c;->i(Ljava/lang/String;Li9/a;)V

    return-void
.end method

.method private zc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V
    .registers 26

    move-object/from16 v0, p2

    move/from16 v1, p3

    const/4 v2, 0x4

    new-array v7, v2, [Ljava/lang/Object;

    const/4 v9, 0x0

    aput-object p1, v7, v9

    const/4 v10, 0x1

    aput-object v0, v7, v10

    invoke-static/range {p3 .. p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const/4 v11, 0x2

    aput-object v3, v7, v11

    const/4 v12, 0x3

    aput-object p4, v7, v12

    const-string v4, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v5, "exceptionOrderHandle"

    const-string v6, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V"

    const/4 v8, 0x2

    move-object/from16 v3, p0

    invoke-static/range {v3 .. v8}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v13, v2, [Ljava/lang/Object;

    aput-object p1, v13, v9

    aput-object v0, v13, v10

    new-instance v3, Ljava/lang/Integer;

    invoke-direct {v3, v1}, Ljava/lang/Integer;-><init>(I)V

    aput-object v3, v13, v11

    aput-object p4, v13, v12

    sget-object v15, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v3, v2, [Ljava/lang/Class;

    const-class v4, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    aput-object v4, v3, v9

    const-class v4, Ljava/lang/String;

    aput-object v4, v3, v10

    sget-object v4, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v4, v3, v11

    const-class v4, Lcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;

    aput-object v4, v3, v12

    sget-object v19, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/16 v16, 0x0

    const v17, 0x9f83

    move-object/from16 v14, p0

    move-object/from16 v18, v3

    invoke-static/range {v13 .. v19}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v3

    iget-boolean v3, v3, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v3, :cond_5a

    return-void

    :cond_5a
    const-string v3, ""

    if-ne v1, v2, :cond_a5

    if-eqz p4, :cond_96

    invoke-virtual/range {p4 .. p4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;->getRedeliveryText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual/range {p4 .. p4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;->getRedeliveryTime()Ljava/lang/String;

    move-result-object v4

    invoke-static {v2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_96

    new-instance v3, Landroid/text/SpannableString;

    invoke-direct {v3, v2}, Landroid/text/SpannableString;-><init>(Ljava/lang/CharSequence;)V

    invoke-static {v4}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_96

    invoke-virtual {v2, v4}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v2

    const/4 v5, -0x1

    if-eq v2, v5, :cond_96

    new-instance v5, Landroid/text/style/ForegroundColorSpan;

    const v6, 0x7f0603c7

    invoke-static {v6}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getColor(I)I

    move-result v6

    invoke-direct {v5, v6}, Landroid/text/style/ForegroundColorSpan;-><init>(I)V

    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v4

    add-int/2addr v4, v2

    const/16 v6, 0x11

    invoke-virtual {v3, v5, v2, v4, v6}, Landroid/text/SpannableString;->setSpan(Ljava/lang/Object;III)V

    :cond_96
    const v2, 0x7f120c02

    invoke-static {v2}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v2

    const v4, 0x7f120bff

    invoke-static {v4}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v4

    goto :goto_c4

    :cond_a5
    if-ne v1, v11, :cond_ca

    const v2, 0x7f120bc3

    invoke-static {v2}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v2

    const v4, 0x7f120bc1

    invoke-static {v4}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v4

    if-eqz p1, :cond_c4

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->isAppendOrder()Z

    move-result v5

    if-eqz v5, :cond_c4

    const v3, 0x7f120bc2

    invoke-static {v3}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v3

    :cond_c4
    :goto_c4
    move-object/from16 v20, v3

    move-object v3, v2

    move-object/from16 v2, v20

    goto :goto_cc

    :cond_ca
    move-object v2, v3

    move-object v4, v2

    :goto_cc
    invoke-virtual/range {p0 .. p0}, Landroid/app/Activity;->isFinishing()Z

    move-result v5

    if-nez v5, :cond_109

    new-instance v5, Lo2/b$a;

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v6

    invoke-direct {v5, v6}, Lo2/b$a;-><init>(Landroidx/fragment/app/FragmentManager;)V

    invoke-virtual {v5, v3}, Lo2/b$a;->O(Ljava/lang/String;)Lo2/b$a;

    move-result-object v3

    const v5, 0x7f1201da

    invoke-static {v5}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getString(I)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Lo2/b$a;->b(Ljava/lang/String;)Lo2/b$a;

    move-result-object v3

    invoke-virtual {v3, v4}, Lo2/b$a;->i(Ljava/lang/String;)Lo2/b$a;

    move-result-object v3

    invoke-virtual {v3, v2}, Lo2/b$a;->j(Ljava/lang/CharSequence;)Lo2/b$a;

    move-result-object v2

    invoke-virtual {v2, v9}, Lo2/b$a;->h(Z)Lo2/b$a;

    move-result-object v2

    new-instance v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$c;

    move-object/from16 v4, p0

    invoke-direct {v3, v4, v0, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$c;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Ljava/lang/String;I)V

    invoke-virtual {v2, v3}, Lo2/b$a;->c(Lcn/yonghui/base/ui/dialog/FeedBackDialogFragment$a;)Lo2/b$a;

    move-result-object v0

    invoke-virtual {v0}, Lo2/b$a;->a()Lo2/b;

    move-result-object v0

    invoke-virtual {v0}, Lo2/b;->i()V

    goto :goto_10b

    :cond_109
    move-object/from16 v4, p0

    :goto_10b
    return-void
.end method


# virtual methods
.method public A5()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f7c

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-virtual {v0}, Landroid/widget/TextView;->getVisibility()I

    move-result v0

    if-eqz v0, :cond_20

    return-void

    :cond_20
    iget-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->T:Z

    if-eqz v0, :cond_3a

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    const v2, 0x7f120424

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const v1, 0x7f060198

    goto :goto_4f

    :cond_3a
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    const v2, 0x7f120810

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const v1, 0x7f06006a

    :goto_4f
    invoke-static {v1}, Lcn/yonghui/hyd/lib/utils/util/ResourceUtil;->getColor(I)I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    return-void
.end method

.method public C3(Z)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f7b

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_25

    return-void

    :cond_25
    if-nez p1, :cond_2e

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {p1, v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->B(Landroidx/recyclerview/widget/RecyclerView;)V

    :cond_2e
    return-void
.end method

.method public C7(Ljava/lang/String;)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f62

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1e

    return-void

    :cond_1e
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const-string v1, "orderRank"

    invoke-virtual {v0, v1, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->getAnalyticsDisplayName()Ljava/lang/String;

    move-result-object p1

    const-string v1, "deliver_comment"

    invoke-static {p0, v1, v0, p1}, Lcn/yonghui/hyd/lib/style/Navigation;->startFlutterSchema(Landroid/content/Context;Ljava/lang/String;Ljava/util/HashMap;Ljava/lang/String;)V

    return-void
.end method

.method public D0(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;)V
    .registers 9

    const/4 v0, 0x1

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p1, v5, v0

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "setProductData"

    const-string v4, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;)V"

    const/4 v6, 0x1

    move-object v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    iput-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q1:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderProductsInfo;

    return-void
.end method

.method public D6(Lcn/yonghui/hyd/order/base/RecommendBean;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "isShowShareEnvelope"

    const-string v3, "(Lcn/yonghui/hyd/order/base/RecommendBean;)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/base/RecommendBean;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f55

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    if-eqz p1, :cond_62

    iput-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k:Lcn/yonghui/hyd/order/base/RecommendBean;

    invoke-virtual {p1}, Lcn/yonghui/hyd/order/base/RecommendBean;->getSharebuoyurl()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-nez v0, :cond_44

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {p1}, Lcn/yonghui/hyd/order/base/RecommendBean;->getSharebuoyurl()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcn/yonghui/base/ui/fresco/FrescoImageLoaderView;->setImageByUrl(Ljava/lang/String;)V

    :cond_44
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0, v7}, Landroid/widget/ImageView;->setVisibility(I)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    new-instance v1, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$n;

    invoke-direct {v1, p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$n;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const-string v1, "yh_elementName"

    const-string v2, "拼手气浮标"

    invoke-static {v0, v1, v2}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-static {v0}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewOnExpo(Landroid/view/View;)V

    goto :goto_69

    :cond_62
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    :goto_69
    return-void
.end method

.method public G3()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f77

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->t(Ljava/lang/String;)V

    return-void
.end method

.method public M9(Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;)V
    .registers 19

    move-object/from16 v7, p0

    move-object/from16 v8, p1

    const/4 v9, 0x1

    new-array v4, v9, [Ljava/lang/Object;

    const/4 v10, 0x0

    aput-object v8, v4, v10

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "setOrdermapInfo"

    const-string v3, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;)V"

    const/4 v5, 0x1

    move-object/from16 v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v9, [Ljava/lang/Object;

    aput-object v8, v0, v10

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v9, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;

    aput-object v1, v5, v10

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f57

    move-object/from16 v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_33

    return-void

    :cond_33
    if-nez v8, :cond_36

    return-void

    :cond_36
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->v:Lcn/yonghui/hyd/coreui/widget/IconFont;

    new-instance v1, Lko/q;

    invoke-direct {v1, v7}, Lko/q;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getCarrier()Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp$Location;

    move-result-object v0

    const/16 v1, 0x8

    if-nez v0, :cond_62

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getRemindmsg()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_53

    goto :goto_62

    :cond_53
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->w:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v10}, Landroid/widget/LinearLayout;->setVisibility(I)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->x:Landroid/widget/TextView;

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getRemindmsg()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_67

    :cond_62
    :goto_62
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->w:Landroid/widget/LinearLayout;

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    :goto_67
    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getShowmap()Ljava/lang/Integer;

    move-result-object v0

    const/4 v2, 0x0

    if-eqz v0, :cond_1d5

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getShowmap()Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    if-ne v0, v9, :cond_1d5

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getCarrierHealth()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_8f

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->K:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;

    const/4 v3, 0x0

    invoke-virtual {v0, v3}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;->f(F)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->A:Landroid/view/View;

    const/4 v3, 0x4

    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    goto :goto_ad

    :cond_8f
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->K:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;

    const/high16 v3, 0x42480000  # 50.0f

    invoke-virtual {v0, v3}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;->f(F)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->A:Landroid/view/View;

    invoke-virtual {v0, v10}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->B:Landroid/widget/TextView;

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getCarrierHealth()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->C:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getCarrierHealthIcon()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Lcn/yonghui/base/ui/fresco/FrescoImageLoaderView;->setImageByUrl(Ljava/lang/String;)V

    :goto_ad
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->F:Luo/k;

    if-eqz v0, :cond_ba

    iget-object v3, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    invoke-virtual {v3}, Lcn/yonghui/hyd/order/detail/c;->v()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    move-result-object v3

    invoke-virtual {v0, v8, v3}, Luo/k;->t(Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V

    :cond_ba
    iget-boolean v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->V:Z

    if-eqz v0, :cond_c9

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    if-eqz v0, :cond_c9

    iput-boolean v10, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->V:Z

    invoke-virtual {v0}, Ljava/util/Timer;->cancel()V

    iput-object v2, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    :cond_c9
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    if-nez v0, :cond_102

    new-instance v0, Ljava/util/Timer;

    invoke-direct {v0}, Ljava/util/Timer;-><init>()V

    iput-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    new-instance v0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$f;

    invoke-direct {v0, v7}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$f;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    iput-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->o1:Ljava/util/TimerTask;

    const/16 v0, 0x7530

    const/16 v2, 0x2710

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getThreshold()Ljava/lang/Integer;

    move-result-object v3

    if-eqz v3, :cond_f8

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getThreshold()Ljava/lang/Integer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-lez v3, :cond_f8

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getThreshold()Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    move v2, v0

    :cond_f8
    iget-object v11, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    iget-object v12, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->o1:Ljava/util/TimerTask;

    int-to-long v13, v2

    int-to-long v2, v0

    move-wide v15, v2

    invoke-virtual/range {v11 .. v16}, Ljava/util/Timer;->schedule(Ljava/util/TimerTask;JJ)V

    :cond_102
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;->a()Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->isMapShowing()Z

    move-result v0

    if-nez v0, :cond_11b

    iget-boolean v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->U:Z

    if-nez v0, :cond_11b

    iput-boolean v9, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->U:Z

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    iget-object v2, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v0, v2}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->k(Landroidx/recyclerview/widget/RecyclerView;)V

    :cond_11b
    invoke-direct/range {p0 .. p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Fc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;)V

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getResourseInfo()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_1cf

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getResourseInfo()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1cf

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getResourseInfo()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0, v10}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v1, v10}, Landroid/widget/ImageView;->setVisibility(I)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/map/OrderDeliveryMapResp;->getResourseInfo()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2, v10}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getActionImage()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcn/yonghui/base/ui/fresco/FrescoImageLoaderView;->setImageByUrl(Ljava/lang/String;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    new-instance v2, Lko/s;

    invoke-direct {v2, v7, v0}, Lko/s;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;)V

    invoke-virtual {v1, v2}, Landroid/widget/ImageView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getModuleName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_moduleName"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getActivityPageName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_positionName"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    iget-object v2, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getOrderpayinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderPayInfo;

    move-result-object v2

    invoke-virtual {v2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderPayInfo;->getTotalpayment()Ljava/lang/Integer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_orderAmt"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    iget-object v2, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const-string v3, "yh_orderId"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getRemarkName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_remarkName"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getContentName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_contentName"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getActivityPageId()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_activityPageId"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getAction()Ljava/lang/String;

    move-result-object v2

    const-string v3, "yh_activityUrl"

    invoke-static {v1, v3, v2}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderDetailsResourceInfo;->getUnitOrderOwner()Ljava/lang/String;

    move-result-object v0

    const-string v2, "yh_owner"

    invoke-static {v1, v2, v0}, Lcn/yonghui/analytics/sdk/util/AnalyticsViewTagHelper;->addTrackParam(Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const-string v1, ""

    const-string v2, "yh_elementExpo"

    invoke-static {v0, v1, v2}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->trackViewEvent(Landroid/view/View;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1fb

    :cond_1cf
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    goto :goto_1fb

    :cond_1d5
    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->z:Lcn/yonghui/hyd/rainy/weahter_bg_plug/WeatherView;

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->A:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    if-eqz v0, :cond_1e8

    invoke-virtual {v0}, Ljava/util/Timer;->cancel()V

    iput-object v2, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    :cond_1e8
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;->a()Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->isMapShowing()Z

    move-result v0

    if-eqz v0, :cond_1fb

    iget-object v0, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    iget-object v1, v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->C(Landroidx/recyclerview/widget/RecyclerView;)V

    :cond_1fb
    :goto_1fb
    return-void
.end method

.method public N3(Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "showOrderRefundConfirmDialog"

    const-string v3, "(Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f69

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;->g:Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;

    iget-object v1, p1, Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;->reasons:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->e(Ljava/util/List;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->f(Ldm/d;)V

    new-instance v1, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;

    invoke-direct {v1}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;-><init>()V

    new-instance v2, Landroid/os/Bundle;

    invoke-direct {v2}, Landroid/os/Bundle;-><init>()V

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->d()Ljava/lang/String;

    move-result-object v3

    const v4, 0x7f120c07

    invoke-virtual {p0, v4}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->a()Ljava/lang/String;

    move-result-object v0

    iget-object v3, p1, Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;->actionname:Ljava/lang/String;

    invoke-virtual {v2, v0, v3}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->beginTransaction()Landroidx/fragment/app/r;

    move-result-object v0

    const-class v2, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Landroidx/fragment/app/DialogFragment;->show(Landroidx/fragment/app/r;Ljava/lang/String;)I

    return-void
.end method

.method public Na()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f76

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    new-instance v1, Lo2/b$a;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v2

    invoke-direct {v1, v2}, Lo2/b$a;-><init>(Landroidx/fragment/app/FragmentManager;)V

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    const v3, 0x7f120bde

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lo2/b$a;->O(Ljava/lang/String;)Lo2/b$a;

    move-result-object v1

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    const v3, 0x7f1201da

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lo2/b$a;->b(Ljava/lang/String;)Lo2/b$a;

    move-result-object v1

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    const v3, 0x7f120bdd

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lo2/b$a;->i(Ljava/lang/String;)Lo2/b$a;

    move-result-object v1

    invoke-virtual {v1, v0}, Lo2/b$a;->h(Z)Lo2/b$a;

    move-result-object v0

    new-instance v1, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$i;

    invoke-direct {v1, p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$i;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Lo2/b$a;->c(Lcn/yonghui/base/ui/dialog/FeedBackDialogFragment$a;)Lo2/b$a;

    move-result-object v0

    invoke-virtual {v0}, Lo2/b$a;->a()Lo2/b;

    move-result-object v0

    invoke-virtual {v0}, Lo2/b;->i()V

    return-void
.end method

.method public P0()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f63

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    const/4 v1, 0x1

    invoke-direct {p0, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->fd(Z)V

    invoke-direct {p0, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->gd(I)V

    return-void
.end method

.method public R2(Z)V
    .registers 2

    iput-boolean p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->T:Z

    return-void
.end method

.method public V6(Z)V
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v8, 0x0

    aput-object v2, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v8

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f66

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_23

    return-void

    :cond_23
    if-eqz p1, :cond_26

    goto :goto_27

    :cond_26
    const/4 v8, 0x4

    :goto_27
    invoke-direct {p0, v8}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->hd(I)V

    return-void
.end method

.method public X0()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f5d

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    if-eqz v0, :cond_20

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->w(Landroidx/recyclerview/widget/RecyclerView;)V

    :cond_20
    return-void
.end method

.method public a1()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f3e

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    const/4 v1, 0x2

    invoke-direct {p0, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->gd(I)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    if-eqz v1, :cond_24

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v1, v2, v0}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    :cond_24
    return-void
.end method

.method public a5(Z)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f7e

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_25

    return-void

    :cond_25
    if-eqz p1, :cond_2e

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {p1, v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->q(Landroidx/recyclerview/widget/RecyclerView;)V

    :cond_2e
    return-void
.end method

.method public a9()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f64

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->gd(I)V

    return-void
.end method

.method public b0(I)V
    .registers 2

    return-void
.end method

.method public b3(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;I)V
    .registers 12

    const/4 v6, 0x2

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v8, 0x1

    aput-object v0, v4, v8

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "showOrderExceptionHandleDialog"

    const-string v3, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;I)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    new-instance v1, Ljava/lang/Integer;

    invoke-direct {v1, p2}, Ljava/lang/Integer;-><init>(I)V

    aput-object v1, v0, v8

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    aput-object v1, v5, v7

    sget-object v1, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v1, v5, v8

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f7d

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_3f

    return-void

    :cond_3f
    const/4 v0, 0x4

    if-ne p2, v0, :cond_48

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-direct {p0, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->yc(Ljava/lang/String;)V

    goto :goto_4e

    :cond_48
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const/4 v1, 0x0

    invoke-direct {p0, p1, v0, p2, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->zc(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Ljava/lang/String;ILcn/yonghui/hyd/order/detail/orderdetailmodel/ExceptionOrderCheckResponse;)V

    :goto_4e
    return-void
.end method

.method public bd()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f6a

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    invoke-virtual {v1, v0, v0}, Lro/c;->v(II)V

    return-void
.end method

.method public c8(Ljava/lang/String;)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f56

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1e

    return-void

    :cond_1e
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    invoke-virtual {v0, p1}, Lcn/yonghui/hyd/order/detail/c;->G(Ljava/lang/String;)V

    return-void
.end method

.method public cd(Z)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f6f

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_25

    return-void

    :cond_25
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->p1:Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;

    if-nez v0, :cond_30

    new-instance v0, Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;

    invoke-direct {v0}, Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;-><init>()V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->p1:Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;

    :cond_30
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->p1:Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    new-instance v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$h;

    invoke-direct {v2, p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$h;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1, p1, v2}, Lcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils;->recordViewShowCount(Landroidx/recyclerview/widget/RecyclerView;ZLcn/yonghui/hyd/lib/helper/RecyclerViewTrackShowUtils$OnExposureListener;)V

    return-void
.end method

.method public ed()V
    .registers 14

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f40

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    const-wide/16 v1, 0x3e8

    :try_start_19
    iget-object v3, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k1:Landroid/os/Handler;

    sget v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->w1:I

    invoke-virtual {v3, v4}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v3, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    invoke-virtual {v3}, Luo/h;->x()Ljava/util/List;

    move-result-object v3

    iget-object v4, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    const/4 v5, -0x1

    if-eqz v4, :cond_60

    invoke-virtual {v4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v4

    if-eqz v4, :cond_60

    iget-object v4, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v4

    invoke-virtual {v4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getPayTime()J

    move-result-wide v4

    invoke-static {}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getDefault()Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;

    move-result-object v6

    invoke-virtual {v6}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getTimeStamp()J

    move-result-wide v6

    sub-long/2addr v4, v6

    div-long/2addr v4, v1

    long-to-int v5, v4

    iget-object v4, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v4

    invoke-virtual {v4}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getGroupEnd()Ljava/lang/Long;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Long;->longValue()J

    move-result-wide v6

    invoke-static {}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getDefault()Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;

    move-result-object v4

    invoke-virtual {v4}, Lcn/yunchuang/android/corehttp/util/TimeSyncUtil;->getTimeStamp()J

    move-result-wide v8

    sub-long/2addr v6, v8

    div-long/2addr v6, v1

    long-to-int v4, v6

    goto :goto_61

    :cond_60
    const/4 v4, -0x1

    :goto_61
    iget-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    if-nez v6, :cond_6c

    new-instance v6, Lzo/p;

    invoke-direct {v6}, Lzo/p;-><init>()V

    iput-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    :cond_6c
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v6

    const/4 v7, 0x1

    if-nez v6, :cond_ee

    iget-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v6}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    move-result-object v6

    check-cast v6, Landroidx/recyclerview/widget/StaggeredGridLayoutManager;

    if-nez v6, :cond_7e

    return-void

    :cond_7e
    const/4 v8, 0x0

    invoke-virtual {v6, v8}, Landroidx/recyclerview/widget/StaggeredGridLayoutManager;->c0([I)[I

    move-result-object v9

    aget v9, v9, v0

    invoke-virtual {v6, v8}, Landroidx/recyclerview/widget/StaggeredGridLayoutManager;->f0([I)[I

    move-result-object v6

    aget v6, v6, v0

    :goto_8b
    add-int/lit8 v8, v6, 0x1

    if-ge v9, v8, :cond_ee

    invoke-interface {v3, v9}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;

    invoke-virtual {v8}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;->getType()I

    move-result v10

    if-ne v10, v7, :cond_eb

    invoke-virtual {v8}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;->getData()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    invoke-virtual {v8}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getGroupEnd()Ljava/lang/Long;

    move-result-object v10

    if-eqz v10, :cond_eb

    if-gez v5, :cond_ab

    if-lez v4, :cond_eb

    :cond_ab
    if-gt v9, v6, :cond_eb

    iget-object v10, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v10, v9}, Landroidx/recyclerview/widget/RecyclerView;->c0(I)Landroidx/recyclerview/widget/RecyclerView$e0;

    move-result-object v10

    check-cast v10, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;

    if-nez v10, :cond_b8

    goto :goto_eb

    :cond_b8
    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->isAtyAlive()Z

    move-result v3

    if-eqz v3, :cond_cf

    if-lez v5, :cond_cf

    invoke-virtual {v10}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->getItemTitle()Landroid/widget/TextView;

    move-result-object v3

    invoke-virtual {v8}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getPayTime()J

    move-result-wide v11

    invoke-virtual {v10, v11, v12}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->getPayEndTimeText(J)Landroid/text/SpannableStringBuilder;

    move-result-object v6

    invoke-virtual {v3, v6}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_cf
    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->isAtyAlive()Z

    move-result v3

    if-eqz v3, :cond_ee

    if-lez v4, :cond_ee

    invoke-virtual {v10}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->getItemTitle()Landroid/widget/TextView;

    move-result-object v3

    invoke-virtual {v8}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getGroupEnd()Ljava/lang/Long;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/Long;->longValue()J

    move-result-wide v8

    invoke-virtual {v10, v8, v9}, Lcn/yonghui/hyd/order/detail/view/orderdetailviewholder/OrderdetailStatusViewholder;->getSpellGroupTimeText(J)Landroid/text/SpannableStringBuilder;

    move-result-object v6

    invoke-virtual {v3, v6}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_ee

    :cond_eb
    :goto_eb
    add-int/lit8 v9, v9, 0x1

    goto :goto_8b

    :cond_ee
    :goto_ee
    const/4 v3, 0x0

    :goto_ef
    iget-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v6}, Ljava/util/ArrayList;->size()I

    move-result v6

    if-ge v3, v6, :cond_181

    if-lez v5, :cond_10e

    iget-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->o:Landroid/widget/TextView;

    iget-object v8, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    iget-object v9, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v9}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v9

    invoke-virtual {v9}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getPayTime()J

    move-result-wide v9

    invoke-virtual {v8, v9, v10}, Lzo/p;->a(J)Landroid/text/SpannableStringBuilder;

    move-result-object v8

    invoke-virtual {v6, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_10e
    if-lez v4, :cond_129

    iget-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->o:Landroid/widget/TextView;

    iget-object v8, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    iget-object v9, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v9}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v9

    invoke-virtual {v9}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->getGroupEnd()Ljava/lang/Long;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/Long;->longValue()J

    move-result-wide v9

    invoke-virtual {v8, v9, v10}, Lzo/p;->c(J)Landroid/text/SpannableStringBuilder;

    move-result-object v8

    invoke-virtual {v6, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    :cond_129
    iget-object v6, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->s1:Ljava/util/ArrayList;

    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;

    iget-object v8, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v8}, Landroid/widget/LinearLayout;->getChildCount()I

    move-result v8

    sub-int/2addr v8, v7

    sub-int/2addr v8, v3

    iget-object v9, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    invoke-virtual {v9, v8}, Landroid/widget/LinearLayout;->getChildAt(I)Landroid/view/View;

    move-result-object v8

    check-cast v8, Landroidx/constraintlayout/widget/ConstraintLayout;

    invoke-virtual {v8, v0}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    move-result-object v8

    check-cast v8, Lcn/yonghui/hyd/lib/style/widget/SubmitButton;

    if-eqz v8, :cond_17d

    if-nez v6, :cond_14c

    goto :goto_17d

    :cond_14c
    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActiontype()I

    move-result v9

    sget-object v10, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->Companion:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;

    invoke-virtual {v10}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->w()I

    move-result v10

    if-ne v9, v10, :cond_17d

    if-lez v5, :cond_17d

    const/16 v9, 0xe10

    if-le v5, v9, :cond_163

    invoke-static {p0, v5}, Lcn/yonghui/hyd/lib/style/UiUtil;->secondsToOrderSecond(Landroid/content/Context;I)Ljava/lang/String;

    move-result-object v9

    goto :goto_167

    :cond_163
    invoke-static {p0, v5}, Lcn/yonghui/hyd/lib/style/UiUtil;->secondsCountdownToOrderSecond(Landroid/content/Context;I)Ljava/lang/String;

    move-result-object v9

    :goto_167
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$ActionInfo;->getActionname()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v8, v6}, Lcn/yonghui/hyd/lib/style/widget/SubmitButton;->setInnerText(Ljava/lang/CharSequence;)V

    :cond_17d
    :goto_17d
    add-int/lit8 v3, v3, 0x1

    goto/16 :goto_ef

    :cond_181
    if-eqz v5, :cond_185

    if-nez v4, :cond_188

    :cond_185
    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->onRefresh()V

    :cond_188
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k1:Landroid/os/Handler;

    sget v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->w1:I

    invoke-virtual {v0, v3, v1, v2}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z
    :try_end_18f
    .catch Ljava/lang/Exception; {:try_start_19 .. :try_end_18f} :catch_190

    goto :goto_197

    :catch_190
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k1:Landroid/os/Handler;

    sget v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->w1:I

    invoke-virtual {v0, v3, v1, v2}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    :goto_197
    return-void
.end method

.method public getAnalyticsDisplayName()Ljava/lang/String;
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v7, Ljava/lang/String;

    const/4 v4, 0x0

    const v5, 0x9f6c

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v1, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_1b

    iget-object v0, v0, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    return-object v0

    :cond_1b
    const v0, 0x7f120f15

    invoke-virtual {p0, v0}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getContext()Landroidx/appcompat/app/AppCompatActivity;
    .registers 1

    return-object p0
.end method

.method public getMainContentResId()I
    .registers 2

    const v0, 0x7f0c007f

    return v0
.end method

.method public getStatisticsPageParams(Z)Ljava/util/Map;
    .registers 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    const-class v7, Ljava/util/Map;

    const/4 v0, 0x0

    const v5, 0x9f6d

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v1, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_29

    iget-object p1, v0, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast p1, Ljava/util/Map;

    return-object p1

    :cond_29
    invoke-super {p0, p1}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getStatisticsPageParams(Z)Ljava/util/Map;

    move-result-object v0

    if-eqz p1, :cond_41

    invoke-static {}, Lcn/yonghui/hyd/lib/utils/auth/AuthManager;->getInstance()Lcn/yonghui/hyd/lib/utils/auth/AuthManager;

    move-result-object p1

    invoke-virtual {p1}, Lcn/yonghui/hyd/lib/utils/auth/AuthManager;->login()Z

    move-result p1

    if-eqz p1, :cond_3c

    const-string p1, "是"

    goto :goto_3e

    :cond_3c
    const-string p1, "否"

    :goto_3e
    const-string v1, "yh_isLogin"

    goto :goto_45

    :cond_41
    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const-string v1, "yh_orderId"

    :goto_45
    invoke-interface {v0, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v0
.end method

.method public initView()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f43

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Bc()V

    invoke-static {p0}, Lzw/a;->e(Ljava/lang/Object;)V

    invoke-static {p0}, Lsd/h;->e(Landroid/app/Activity;)V

    const v0, 0x7f09011a

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/baidu/mapapi/map/MapView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    const v0, 0x7f090ce8

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->R:Landroid/view/View;

    const v0, 0x7f090ca8

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    const v0, 0x7f090ced

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Y:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const v0, 0x7f090cec

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    new-instance v1, Luo/k;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-direct {v1, p0, v2}, Luo/k;-><init>(Landroid/content/Context;Lcom/baidu/mapapi/map/MapView;)V

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->F:Luo/k;

    const v1, 0x7f090eed

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/widget/RelativeLayout;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->l:Landroid/widget/RelativeLayout;

    const v1, 0x7f090eee

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/widget/TextView;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->m:Landroid/widget/TextView;

    const v1, 0x7f090eec

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/coreui/widget/IconFont;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const v1, 0x7f090e97

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/widget/TextView;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->o:Landroid/widget/TextView;

    const v1, 0x7f090106

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/coreui/widget/IconFont;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->p:Lcn/yonghui/hyd/coreui/widget/IconFont;

    new-instance v2, Lko/p;

    invoke-direct {v2, p0}, Lko/p;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    const v1, 0x7f090edd

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/coreui/widget/IconFont;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const v1, 0x7f0915e1

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/widget/TextView;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r:Landroid/widget/TextView;

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Gc()V

    const v1, 0x7f090c8c

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->l1:Landroid/view/View;

    const v1, 0x7f090662

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroid/view/ViewStub;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->D:Landroid/view/ViewStub;

    const v1, 0x7f090ba8

    invoke-virtual {p0, v1}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    check-cast v1, Landroidx/recyclerview/widget/RecyclerView;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Landroidx/recyclerview/widget/RecyclerView;->setNestedScrollingEnabled(Z)V

    new-instance v1, Landroidx/lifecycle/v0;

    invoke-direct {v1, p0}, Landroidx/lifecycle/v0;-><init>(Landroidx/lifecycle/y0;)V

    const-class v3, Lro/c;

    invoke-virtual {v1, v3}, Landroidx/lifecycle/v0;->a(Ljava/lang/Class;)Landroidx/lifecycle/s0;

    move-result-object v1

    check-cast v1, Lro/c;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    new-instance v1, Landroidx/lifecycle/v0;

    invoke-direct {v1, p0}, Landroidx/lifecycle/v0;-><init>(Landroidx/lifecycle/y0;)V

    const-class v3, Lw9/b;

    invoke-virtual {v1, v3}, Landroidx/lifecycle/v0;->a(Ljava/lang/Class;)Landroidx/lifecycle/s0;

    move-result-object v1

    check-cast v1, Lw9/b;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->b:Lw9/b;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    new-instance v3, Landroidx/recyclerview/widget/StaggeredGridLayoutManager;

    const/4 v4, 0x2

    invoke-direct {v3, v4, v2}, Landroidx/recyclerview/widget/StaggeredGridLayoutManager;-><init>(II)V

    invoke-virtual {v1, v3}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    new-instance v1, Lko/w;

    invoke-direct {v1}, Lko/w;-><init>()V

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->S:Lko/w;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    new-instance v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$d;

    invoke-direct {v3, p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$d;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v1, v3}, Landroidx/recyclerview/widget/RecyclerView;->m(Landroidx/recyclerview/widget/RecyclerView$t;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v1}, Landroid/view/ViewGroup;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v1

    check-cast v1, Landroidx/coordinatorlayout/widget/CoordinatorLayout$g;

    invoke-virtual {v1}, Landroidx/coordinatorlayout/widget/CoordinatorLayout$g;->f()Landroidx/coordinatorlayout/widget/CoordinatorLayout$c;

    move-result-object v1

    check-cast v1, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    if-eqz v1, :cond_126

    invoke-virtual {v1, p0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->x(Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior$c;)V

    :cond_126
    invoke-virtual {v0}, Landroid/widget/LinearLayout;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v0

    check-cast v0, Landroidx/coordinatorlayout/widget/CoordinatorLayout$g;

    invoke-virtual {v0}, Landroidx/coordinatorlayout/widget/CoordinatorLayout$g;->f()Landroidx/coordinatorlayout/widget/CoordinatorLayout$c;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->K:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrdetailMapResourceBehavior;

    const v0, 0x7f090915

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const v0, 0x7f090ceb

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/coreui/widget/IconFont;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->v:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const v0, 0x7f090ce9

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->w:Landroid/widget/LinearLayout;

    const v0, 0x7f090cea

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->x:Landroid/widget/TextView;

    const v0, 0x7f090c1a

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->A:Landroid/view/View;

    const v0, 0x7f091829

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->B:Landroid/widget/TextView;

    const v0, 0x7f09153f

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->C:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const v0, 0x7f091a82

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->y:Landroid/view/View;

    const v0, 0x7f090e8d

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroidx/constraintlayout/widget/ConstraintLayout;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Z:Landroidx/constraintlayout/widget/ConstraintLayout;

    const v0, 0x7f09017c

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e1:Landroid/widget/LinearLayout;

    const v0, 0x7f090e8f

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f1:Landroid/widget/TextView;

    const v0, 0x7f09017a

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g1:Landroid/widget/LinearLayout;

    const v0, 0x7f090179

    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/LinearLayout;

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->h1:Landroid/widget/LinearLayout;

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f1:Landroid/widget/TextView;

    const/16 v1, 0x28

    invoke-static {v0, v1}, Lex/i;->q(Landroid/view/View;I)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f1:Landroid/widget/TextView;

    new-instance v1, Lko/o;

    invoke-direct {v1, p0}, Lko/o;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g1:Landroid/widget/LinearLayout;

    new-instance v1, Lko/n;

    invoke-direct {v1, p0}, Lko/n;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->l:Landroid/widget/RelativeLayout;

    new-instance v1, Lko/e;

    invoke-direct {v1, p0}, Lko/e;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/RelativeLayout;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    new-instance v0, Lcn/yonghui/hyd/order/detail/c;

    invoke-direct {v0, p0}, Lcn/yonghui/hyd/order/detail/c;-><init>(Lko/c;)V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->b:Lw9/b;

    invoke-virtual {v0}, Lw9/b;->b()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->w(Ljava/lang/String;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->W:Lcn/yonghui/hyd/lib/view/GoPushSettingView;

    if-nez v0, :cond_207

    new-instance v0, Lcn/yonghui/hyd/lib/view/GoPushSettingView;

    invoke-direct {v0}, Lcn/yonghui/hyd/lib/view/GoPushSettingView;-><init>()V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->W:Lcn/yonghui/hyd/lib/view/GoPushSettingView;

    :cond_207
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    if-eqz v0, :cond_225

    invoke-virtual {v0}, Lro/c;->o()Lnf/a;

    move-result-object v0

    new-instance v1, Lko/h;

    invoke-direct {v1, p0}, Lko/h;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, p0, v1}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/j0;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    invoke-virtual {v0}, Lro/c;->n()Lnf/a;

    move-result-object v0

    new-instance v1, Lko/f;

    invoke-direct {v1, p0}, Lko/f;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, p0, v1}, Landroidx/lifecycle/LiveData;->j(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/j0;)V

    :cond_225
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    if-nez v0, :cond_230

    new-instance v0, Lzo/p;

    invoke-direct {v0}, Lzo/p;-><init>()V

    iput-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    :cond_230
    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Zc()V

    return-void
.end method

.method public j4(Ljava/lang/String;Ljava/util/ArrayList;)V
    .registers 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/ArrayList<",
            "Lcn/yonghui/hyd/order/detail/update/model/RefundCheckBtn;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x2

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 v3, 0x1

    aput-object p2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    const-class v0, Ljava/util/ArrayList;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f7a

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_27

    return-void

    :cond_27
    const v0, 0x7f120d02

    invoke-virtual {p0, v0}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0, p1, p2}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->vc(Ljava/lang/String;Ljava/lang/String;Ljava/util/ArrayList;)V

    return-void
.end method

.method public l(Lcn/yonghui/hyd/common/member/NewPersonEntranceVO;)V
    .registers 9

    const/4 v0, 0x1

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p1, v5, v0

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "showNewMemberProduct"

    const-string v4, "(Lcn/yonghui/hyd/common/member/NewPersonEntranceVO;)V"

    const/4 v6, 0x1

    move-object v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    return-void
.end method

.method public m6(Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;)V
    .registers 18

    move-object/from16 v0, p1

    const/4 v1, 0x1

    new-array v6, v1, [Ljava/lang/Object;

    const/4 v8, 0x0

    aput-object v0, v6, v8

    const-string v3, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v4, "showDeliveryTimeDialog"

    const-string v5, "(Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;)V"

    const/4 v7, 0x1

    move-object/from16 v2, p0

    invoke-static/range {v2 .. v7}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v9, v1, [Ljava/lang/Object;

    aput-object v0, v9, v8

    sget-object v11, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v14, v1, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;

    aput-object v1, v14, v8

    sget-object v15, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v12, 0x0

    const v13, 0x9f78

    move-object/from16 v10, p0

    invoke-static/range {v9 .. v15}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_31

    return-void

    :cond_31
    if-eqz v0, :cond_60

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;->getDeliverydates()Ljava/util/ArrayList;

    move-result-object v1

    if-eqz v1, :cond_60

    invoke-virtual/range {p1 .. p1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;->getDeliverydates()Ljava/util/ArrayList;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    if-lez v1, :cond_60

    new-instance v1, Lcn/yonghui/hyd/order/detail/view/timeselectdialog/TimeSelectDialog;

    invoke-direct {v1, v0}, Lcn/yonghui/hyd/order/detail/view/timeselectdialog/TimeSelectDialog;-><init>(Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;)V

    new-instance v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$j;

    move-object/from16 v3, p0

    invoke-direct {v2, v3, v0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$j;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/DeliveryDateResponse;)V

    invoke-virtual {v1, v2}, Lcn/yonghui/hyd/order/detail/view/timeselectdialog/TimeSelectDialog;->b9(Lmb0/p;)V

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    const-class v2, Lcn/yonghui/hyd/order/detail/view/timeselectdialog/TimeSelectDialog;

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Lcn/yonghui/hyd/coreui/widget/BaseBottomDialogFragment;->show(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;)V

    goto :goto_62

    :cond_60
    move-object/from16 v3, p0

    :goto_62
    return-void
.end method

.method public nd()Ljava/lang/String;
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v7, Ljava/lang/String;

    const/4 v4, 0x0

    const v5, 0x9f6e

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v2, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v2, :cond_1b

    iget-object v0, v1, Lcom/meituan/robust/PatchProxyResult;->result:Ljava/lang/Object;

    check-cast v0, Ljava/lang/String;

    return-object v0

    :cond_1b
    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusmsg()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_39

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_39

    const-string v2, "¥"

    invoke-virtual {v1, v2}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_39

    invoke-virtual {v1, v2}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v2

    invoke-virtual {v1, v0, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    :cond_39
    return-object v1
.end method

.method public onActivityResult(IILandroid/content/Intent;)V
    .registers 13
    .param p3  # Landroid/content/Intent;
        .annotation build Lf/h0;
        .end annotation
    .end param

    const/4 v0, 0x3

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p1}, Ljava/lang/Integer;-><init>(I)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p2}, Ljava/lang/Integer;-><init>(I)V

    const/4 v8, 0x1

    aput-object v2, v1, v8

    const/4 v2, 0x2

    aput-object p3, v1, v2

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    aput-object v0, v6, v8

    const-class v0, Landroid/content/Intent;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f41

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_36

    return-void

    :cond_36
    invoke-super {p0, p1, p2, p3}, Landroidx/fragment/app/b;->onActivityResult(IILandroid/content/Intent;)V

    sget-object p3, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;->Companion:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;

    invoke-virtual {p3}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo$a;->m()I

    move-result p3

    if-ne p1, p3, :cond_4d

    const/4 p1, -0x1

    if-ne p2, p1, :cond_4d

    invoke-direct {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->wc()V

    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->onRefresh()V

    invoke-virtual {p0, v8}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->showLoadingView(Z)V

    :cond_4d
    return-void
.end method

.method public onBackPressed()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f54

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    iget-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->d:Z

    if-eqz v0, :cond_2d

    new-instance v0, Landroid/util/ArrayMap;

    invoke-direct {v0}, Landroid/util/ArrayMap;-><init>()V

    const-string v1, "fragment"

    const-string v2, "cn.yonghui.hyd.main.ui.cms.home.HomeFragment"

    invoke-virtual {v0, v1, v2}, Landroid/util/ArrayMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "cn.yonghui.hyd.MainActivity"

    invoke-static {p0, v1, v0}, Lcn/yonghui/hyd/lib/utils/plugin/YHRouter;->navigation(Landroid/content/Context;Ljava/lang/String;Ljava/util/Map;)V

    goto :goto_3b

    :cond_2d
    iget v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->O:I

    iget v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->P:I

    if-eq v0, v1, :cond_3b

    new-instance v0, Lap/h;

    invoke-direct {v0}, Lap/h;-><init>()V

    invoke-static {v0}, Lzw/a;->c(Ljava/lang/Object;)V

    :cond_3b
    :goto_3b
    invoke-super {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->onBackPressed()V

    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Landroid/os/Bundle;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f3f

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1e

    return-void

    :cond_1e
    invoke-static {}, Lcn/yonghui/hyd/lib/utils/util/BaiduInitUtil;->getInstance()Z

    invoke-super {p0, p1}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->onCreate(Landroid/os/Bundle;)V

    return-void
.end method

.method public onDestroy()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f53

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    invoke-super {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->onDestroy()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    const/4 v1, 0x0

    if-eqz v0, :cond_24

    invoke-virtual {v0}, Ljava/util/Timer;->cancel()V

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->L:Ljava/util/Timer;

    :cond_24
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k1:Landroid/os/Handler;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    if-eqz v0, :cond_30

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/c;->r()V

    :cond_30
    invoke-static {p0}, Lzw/a;->h(Ljava/lang/Object;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->F:Luo/k;

    if-eqz v0, :cond_3c

    invoke-virtual {v0}, Luo/k;->d()V

    iput-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->F:Luo/k;

    :cond_3c
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-virtual {v0}, Lcom/baidu/mapapi/map/MapView;->getMap()Lcom/baidu/mapapi/map/BaiduMap;

    move-result-object v0

    invoke-virtual {v0}, Lcom/baidu/mapapi/map/BaiduMap;->clear()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-virtual {v0}, Lcom/baidu/mapapi/map/MapView;->onDestroy()V

    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;->a()Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->reset()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->X:Lcn/yonghui/hyd/lib/style/widget/LottieAnimationViewWrapper;

    invoke-virtual {v0}, Lcom/airbnb/lottie/LottieAnimationView;->cancelAnimation()V

    return-void
.end method

.method public onErrorCoverClicked()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f61

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    invoke-super {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->onErrorCoverClicked()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->w(Ljava/lang/String;)V

    invoke-virtual {p0, v2}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->setLoadingContainerVisible(Z)V

    return-void
.end method

.method public onEvent(Lcn/yonghui/hyd/lib/helper/addorder/AddOrderFinishEvent;)V
    .registers 17
    .annotation runtime Lorg/greenrobot/eventbus/c;
    .end annotation

    const/4 v0, 0x1

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v5, v7

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "onEvent"

    const-string v4, "(Lcn/yonghui/hyd/lib/helper/addorder/AddOrderFinishEvent;)V"

    const/4 v6, 0x1

    move-object v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v8, v0, [Ljava/lang/Object;

    aput-object p1, v8, v7

    sget-object v10, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v13, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/lib/helper/addorder/AddOrderFinishEvent;

    aput-object v0, v13, v7

    sget-object v14, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v11, 0x0

    const v12, 0x9f75

    move-object v9, p0

    invoke-static/range {v8 .. v14}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    if-eqz p1, :cond_32

    invoke-virtual {p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->onRefresh()V

    :cond_32
    return-void
.end method

.method public onEvent(Lcn/yonghui/hyd/lib/style/event/RefreshOrderDetailEvent;)V
    .registers 10
    .annotation runtime Lorg/greenrobot/eventbus/c;
    .end annotation

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "onEvent"

    const-string v3, "(Lcn/yonghui/hyd/lib/style/event/RefreshOrderDetailEvent;)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/lib/style/event/RefreshOrderDetailEvent;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f74

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    if-eqz p1, :cond_36

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1, v7}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    :cond_36
    return-void
.end method

.method public onEvent(Lcn/yonghui/hyd/order/event/InvoiceEvent;)V
    .registers 10
    .annotation runtime Lorg/greenrobot/eventbus/c;
    .end annotation

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "onEvent"

    const-string v3, "(Lcn/yonghui/hyd/order/event/InvoiceEvent;)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/order/event/InvoiceEvent;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f6b

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    if-eqz v0, :cond_36

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1, v7}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    :cond_36
    return-void
.end method

.method public onNewIntent(Landroid/content/Intent;)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v4, Landroid/content/Intent;

    aput-object v4, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f4f

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_1e

    return-void

    :cond_1e
    invoke-super {p0, p1}, Landroidx/fragment/app/b;->onNewIntent(Landroid/content/Intent;)V

    const-string v1, "showRiderGifts"

    invoke-virtual {p1, v1}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_37

    invoke-virtual {p1, v1}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "1"

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_37

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->a:Z

    :cond_37
    return-void
.end method

.method public onOrderDeleted(Lcn/yonghui/hyd/order/event/OrderDeletedEvent;)V
    .registers 17
    .annotation runtime Lorg/greenrobot/eventbus/c;
        threadMode = .enum Lorg/greenrobot/eventbus/ThreadMode;->MAIN:Lorg/greenrobot/eventbus/ThreadMode;
    .end annotation

    const/4 v0, 0x1

    new-array v5, v0, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v5, v7

    const-string v2, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v3, "onOrderDeleted"

    const-string v4, "(Lcn/yonghui/hyd/order/event/OrderDeletedEvent;)V"

    const/4 v6, 0x1

    move-object v1, p0

    invoke-static/range {v1 .. v6}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v8, v0, [Ljava/lang/Object;

    aput-object p1, v8, v7

    sget-object v10, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v13, v0, [Ljava/lang/Class;

    const-class v0, Lcn/yonghui/hyd/order/event/OrderDeletedEvent;

    aput-object v0, v13, v7

    sget-object v14, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v11, 0x0

    const v12, 0x9f7f

    move-object v9, p0

    invoke-static/range {v8 .. v14}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public onPause()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f52

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    invoke-super {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->onPause()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-virtual {v0}, Lcom/baidu/mapapi/map/MapView;->onPause()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->S:Lko/w;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    invoke-virtual {v0, v1}, Lko/w;->f(Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;)V

    return-void
.end method

.method public onRefresh()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f65

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    const/4 v1, 0x2

    invoke-direct {p0, v1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->gd(I)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    if-eqz v1, :cond_2a

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->onPageRefresh()V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    const/4 v3, 0x1

    invoke-virtual {v1, v2, v3, v0}, Lcn/yonghui/hyd/order/detail/c;->I(Ljava/lang/String;ZZ)V

    :cond_2a
    return-void
.end method

.method public onResume()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f51

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    invoke-super {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->onResume()V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-virtual {v1}, Lcom/baidu/mapapi/map/MapView;->onResume()V

    iget-boolean v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->M:Z

    if-eqz v1, :cond_38

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lcn/yonghui/hyd/order/detail/c;->u(Ljava/lang/String;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v1, v2, v0}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v1, v2}, Lcn/yonghui/hyd/order/detail/c;->w(Ljava/lang/String;)V

    :cond_38
    iget-boolean v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->h:Z

    if-nez v1, :cond_41

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->W:Lcn/yonghui/hyd/lib/view/GoPushSettingView;

    invoke-virtual {v1, p0, v0}, Lcn/yonghui/hyd/lib/view/GoPushSettingView;->checkPushPermission(Landroid/app/Activity;I)V

    :cond_41
    iget-boolean v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->a:Z

    if-eqz v1, :cond_4c

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v1, v2, v0}, Lcn/yonghui/hyd/order/detail/c;->H(Ljava/lang/String;Z)V

    :cond_4c
    return-void
.end method

.method public onShareResult(Lc9/g;)V
    .registers 10
    .annotation runtime Lorg/greenrobot/eventbus/c;
    .end annotation

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "onShareResult"

    const-string v3, "(Lcn/yonghui/hyd/common/event/ShareResultEvent;)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lc9/g;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f59

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    invoke-virtual {p1}, Lc9/g;->a()Z

    move-result v0

    if-eqz v0, :cond_52

    const v0, 0x7f120fc3

    invoke-virtual {p0, v0}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcn/yonghui/hyd/lib/style/UiUtil;->showToast(Ljava/lang/CharSequence;)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j:Lcn/yonghui/hyd/coreui/widget/imageloader/ImageLoaderView;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setVisibility(I)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    if-eqz v0, :cond_5c

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->k:Lcn/yonghui/hyd/order/base/RecommendBean;

    invoke-virtual {v1}, Lcn/yonghui/hyd/order/base/RecommendBean;->getBunchid()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->J(Ljava/lang/String;)V

    goto :goto_5c

    :cond_52
    const v0, 0x7f120fc2

    invoke-virtual {p0, v0}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcn/yonghui/hyd/lib/style/UiUtil;->showToast(Ljava/lang/CharSequence;)V

    :cond_5c
    :goto_5c
    return-void
.end method

.method public onWindowFocusChanged(Z)V
    .registers 2

    invoke-super {p0, p1}, Landroid/app/Activity;->onWindowFocusChanged(Z)V

    invoke-static {p0, p1}, Lqv/f;->c(Landroid/app/Activity;Z)V

    return-void
.end method

.method public p2(Ljava/util/List;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Ljava/util/List;Z)V
    .registers 21
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;",
            ">;",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;",
            "Ljava/util/List<",
            "Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailPageData;",
            ">;Z)V"
        }
    .end annotation

    move-object/from16 v9, p0

    move-object/from16 v7, p1

    move-object/from16 v10, p2

    move-object/from16 v8, p3

    move/from16 v11, p4

    const/4 v12, 0x4

    new-array v4, v12, [Ljava/lang/Object;

    const/4 v13, 0x0

    aput-object v7, v4, v13

    const/4 v14, 0x1

    aput-object v10, v4, v14

    const/4 v6, 0x2

    aput-object v8, v4, v6

    invoke-static/range {p4 .. p4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const/4 v15, 0x3

    aput-object v0, v4, v15

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "setOrderDetailPageData"

    const-string v3, "(Ljava/util/List;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;Ljava/util/List;Z)V"

    const/4 v5, 0x1

    move-object/from16 v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v12, [Ljava/lang/Object;

    aput-object v7, v0, v13

    aput-object v10, v0, v14

    aput-object v8, v0, v6

    new-instance v1, Ljava/lang/Byte;

    invoke-direct {v1, v11}, Ljava/lang/Byte;-><init>(B)V

    aput-object v1, v0, v15

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v12, [Ljava/lang/Class;

    const-class v1, Ljava/util/List;

    aput-object v1, v5, v13

    const-class v1, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    aput-object v1, v5, v14

    const-class v1, Ljava/util/List;

    aput-object v1, v5, v6

    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v1, v5, v15

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f5f

    move-object/from16 v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_5d

    return-void

    :cond_5d
    iput-boolean v13, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->h:Z

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->W:Lcn/yonghui/hyd/lib/view/GoPushSettingView;

    invoke-virtual {v0, v9, v13}, Lcn/yonghui/hyd/lib/view/GoPushSettingView;->checkPushPermission(Landroid/app/Activity;I)V

    iput-object v7, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->G:Ljava/util/List;

    iput-object v8, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->H:Ljava/util/List;

    iput-object v10, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->I:Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;

    invoke-direct/range {p0 .. p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->dd()V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    invoke-virtual {v0, v10}, Lro/c;->A(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V

    invoke-direct/range {p0 .. p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->id()V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r:Landroid/widget/TextView;

    invoke-virtual/range {p2 .. p2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusmsg()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->j1:Lzo/p;

    iget-object v1, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->o:Landroid/widget/TextView;

    invoke-virtual/range {p2 .. p2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusinfo()Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lzo/p;->d(Landroid/widget/TextView;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderStatusInfo;)V

    invoke-virtual/range {p2 .. p2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getOrdertype()I

    move-result v0

    const/4 v1, 0x5

    if-ne v0, v1, :cond_98

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setVisibility(I)V

    goto :goto_a0

    :cond_98
    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-virtual {v0, v13}, Landroid/widget/TextView;->setVisibility(I)V

    invoke-virtual/range {p0 .. p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->A5()V

    :goto_a0
    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    new-instance v1, Lko/v;

    invoke-direct {v1, v9, v10}, Lko/v;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    if-eqz v0, :cond_15d

    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getItemDecorationCount()I

    move-result v0

    if-lez v0, :cond_b9

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v0, v13}, Landroidx/recyclerview/widget/RecyclerView;->o1(I)V

    :cond_b9
    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    new-instance v1, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$g;

    invoke-direct {v1, v9}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$g;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->h(Landroidx/recyclerview/widget/RecyclerView$o;)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    if-nez v0, :cond_f9

    new-instance v15, Luo/h;

    iget-object v4, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v6

    iget-object v8, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    move-object v0, v15

    move-object/from16 v1, p0

    move-object/from16 v2, p0

    move-object/from16 v3, p1

    move-object/from16 v5, p0

    move-object v7, v8

    move-object/from16 v8, p0

    invoke-direct/range {v0 .. v8}, Luo/h;-><init>(Landroid/content/Context;Lko/c;Ljava/util/List;Ljava/lang/String;Lko/b;Landroidx/fragment/app/FragmentManager;Lro/c;Landroidx/lifecycle/LifecycleOwner;)V

    iput-object v15, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    new-instance v0, Lko/l;

    invoke-direct {v0, v9}, Lko/l;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v15, v0}, Luo/h;->H(Lmb0/p;)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    iget-object v1, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$h;)V

    sget-object v0, Lcn/yonghui/hyd/order/detail/b;->a:Lcn/yonghui/hyd/order/detail/b$a;

    iget-object v1, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/b$a;->a(Landroidx/recyclerview/widget/RecyclerView;)V

    goto :goto_10c

    :cond_f9
    invoke-virtual {v0}, Luo/h;->B()I

    move-result v0

    if-nez v0, :cond_107

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    iget-object v1, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->H:Ljava/util/List;

    invoke-virtual {v0, v1}, Luo/h;->J(Ljava/util/List;)V

    goto :goto_10c

    :cond_107
    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    invoke-virtual {v0, v7}, Luo/h;->J(Ljava/util/List;)V

    :goto_10c
    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    invoke-virtual {v0, v10}, Luo/h;->I(Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->i:Luo/h;

    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$h;->notifyDataSetChanged()V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->J:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;

    if-eqz v0, :cond_11f

    iget-object v1, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->g:Landroidx/recyclerview/widget/RecyclerView;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailListBehavior;->w(Landroidx/recyclerview/widget/RecyclerView;)V

    :cond_11f
    invoke-virtual/range {p2 .. p2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getShowmap()Ljava/lang/Integer;

    move-result-object v0

    if-eqz v0, :cond_14a

    invoke-virtual/range {p2 .. p2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getShowmap()Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    if-ne v0, v14, :cond_14a

    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;->a()Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;

    move-result-object v0

    invoke-virtual {v0, v14}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->setShowMap(Z)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    iget-object v1, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/c;->u(Ljava/lang/String;)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-virtual {v0, v13}, Landroid/view/ViewGroup;->setVisibility(I)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->R:Landroid/view/View;

    invoke-virtual {v0, v13}, Landroid/view/View;->setVisibility(I)V

    goto :goto_15d

    :cond_14a
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->Companion:Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper$b;->a()Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;

    move-result-object v0

    invoke-virtual {v0, v13}, Lcn/yonghui/hyd/order/detail/view/orderdetailbehavior/OrderdetailBehaviorHelper;->setShowMap(Z)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->Q:Lcom/baidu/mapapi/map/MapView;

    invoke-virtual {v0, v12}, Landroid/view/ViewGroup;->setVisibility(I)V

    iget-object v0, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->R:Landroid/view/View;

    invoke-virtual {v0, v12}, Landroid/view/View;->setVisibility(I)V

    :cond_15d
    :goto_15d
    invoke-virtual {v9, v13}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->showLoadingView(Z)V

    invoke-virtual {v9, v13}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->setLoadingContainerVisible(Z)V

    invoke-virtual {v9, v11}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->cd(Z)V

    invoke-virtual/range {p0 .. p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->ed()V

    sget-object v0, Lda/f;->c:Lda/f$b;

    invoke-virtual {v0}, Lda/f$b;->a()Lda/f;

    move-result-object v0

    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-virtual/range {p0 .. p0}, Landroidx/activity/ComponentActivity;->getLifecycle()Landroidx/lifecycle/t;

    move-result-object v2

    iget-object v3, v9, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->c:Ljava/lang/String;

    invoke-virtual/range {p2 .. p2}, Lcn/yonghui/hyd/order/detail/orderdetailmodel/OrderdetailResponse;->getStatusmsg()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v1, v2, v3, v4}, Lda/f;->g(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/t;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public r5(Ljava/util/ArrayList;)V
    .registers 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ArrayList<",
            "Lcn/yonghui/hyd/order/detail/view/cancleorderreason/model/CancelReasonBean;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/util/ArrayList;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f67

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1e

    return-void

    :cond_1e
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    new-instance p1, Lcn/yonghui/hyd/order/detail/view/cancleorderreason/model/CancelReasonBean;

    const v1, 0x7f120bc7

    invoke-virtual {p0, v1}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    const-string v3, ""

    invoke-direct {p1, v1, v3, v2}, Lcn/yonghui/hyd/order/detail/view/cancleorderreason/model/CancelReasonBean;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance p1, Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->f:Lro/c;

    invoke-direct {p1, v0, v1}, Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;-><init>(Ljava/util/List;Lro/c;)V

    iput-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n1:Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

    new-instance v0, Lko/k;

    invoke-direct {v0, p0}, Lko/k;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {p1, v0}, Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;->g9(Lmb0/l;)V

    iget-object p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->n1:Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->beginTransaction()Landroidx/fragment/app/r;

    move-result-object v0

    const-class v1, Lcn/yonghui/hyd/order/detail/view/cancleorderreason/CancelOrderDialog;

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Landroidx/fragment/app/DialogFragment;->show(Landroidx/fragment/app/r;Ljava/lang/String;)I

    return-void
.end method

.method public s4(Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;)V
    .registers 10

    const/4 v6, 0x1

    new-array v4, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    aput-object p1, v4, v7

    const-string v1, "cn/yonghui/hyd/order/detail/OrderDetailActivity"

    const-string v2, "showOrderReturnConfirmDialog"

    const-string v3, "(Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;)V"

    const/4 v5, 0x1

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lcn/yonghui/analytics/sdk/YHAnalyticsAutoTrackHelper;->hookModelSetMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;I)V

    new-array v0, v6, [Ljava/lang/Object;

    aput-object p1, v0, v7

    sget-object v2, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v5, v6, [Ljava/lang/Class;

    const-class v1, Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;

    aput-object v1, v5, v7

    sget-object v6, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v3, 0x0

    const v4, 0x9f68

    move-object v1, p0

    invoke-static/range {v0 .. v6}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_2d

    return-void

    :cond_2d
    sget-object v0, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;->g:Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;

    iget-object v1, p1, Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;->reasons:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->e(Ljava/util/List;)V

    iget-object v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->e:Lcn/yonghui/hyd/order/detail/c;

    invoke-virtual {v0, v1}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->f(Ldm/d;)V

    new-instance v1, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;

    invoke-direct {v1}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;-><init>()V

    new-instance v2, Landroid/os/Bundle;

    invoke-direct {v2}, Landroid/os/Bundle;-><init>()V

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->d()Ljava/lang/String;

    move-result-object v3

    const v4, 0x7f120c08

    invoke-virtual {p0, v4}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0}, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog$a;->a()Ljava/lang/String;

    move-result-object v0

    iget-object v3, p1, Lcn/yonghui/hyd/lib/style/tempmodel/ordermodel/OrderActionModel;->actionname:Ljava/lang/String;

    invoke-virtual {v2, v0, v3}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->beginTransaction()Landroidx/fragment/app/r;

    move-result-object v0

    const-class v2, Lcn/yonghui/hyd/order/detail/view/OrderBaseDialog;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v0, v2}, Landroidx/fragment/app/DialogFragment;->show(Landroidx/fragment/app/r;Ljava/lang/String;)I

    return-void
.end method

.method public s8(I)V
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p1}, Ljava/lang/Integer;-><init>(I)V

    const/4 v8, 0x0

    aput-object v2, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v2, v6, v8

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f73

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_23

    return-void

    :cond_23
    iget v1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->N:I

    if-nez v1, :cond_29

    iput p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->O:I

    :cond_29
    const/4 v1, 0x6

    if-ne p1, v1, :cond_2f

    invoke-direct {p0, v8}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->fd(Z)V

    :cond_2f
    iput p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->P:I

    iget p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->N:I

    add-int/2addr p1, v0

    iput p1, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->N:I

    return-void
.end method

.method public sa()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f5c

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v1

    iget-boolean v1, v1, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v1, :cond_17

    return-void

    :cond_17
    const v1, 0x7f09078e

    :try_start_1a
    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->q:Lcn/yonghui/hyd/coreui/widget/IconFont;

    const/16 v3, 0x8

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setVisibility(I)V

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->r:Landroid/widget/TextView;

    invoke-virtual {v2, v0}, Landroid/widget/TextView;->setVisibility(I)V

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    if-nez v2, :cond_32

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->D:Landroid/view/ViewStub;

    invoke-virtual {v2}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    move-result-object v2

    iput-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;
    :try_end_32
    .catch Ljava/lang/Exception; {:try_start_1a .. :try_end_32} :catch_5c
    .catchall {:try_start_1a .. :try_end_32} :catchall_45

    :cond_32
    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    if-eqz v2, :cond_72

    invoke-virtual {v2, v0}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    new-instance v1, Lko/r;

    invoke-direct {v1, p0}, Lko/r;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    goto :goto_6f

    :catchall_45
    move-exception v2

    iget-object v3, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    if-eqz v3, :cond_5b

    invoke-virtual {v3, v0}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    new-instance v1, Lko/r;

    invoke-direct {v1, p0}, Lko/r;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_5b
    throw v2

    :catch_5c
    nop

    iget-object v2, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    if-eqz v2, :cond_72

    invoke-virtual {v2, v0}, Landroid/view/View;->setVisibility(I)V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->E:Landroid/view/View;

    invoke-virtual {v0, v1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    new-instance v1, Lko/r;

    invoke-direct {v1, p0}, Lko/r;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    :goto_6f
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    :cond_72
    return-void
.end method

.method public setError(I)V
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Integer;

    invoke-direct {v2, p1}, Ljava/lang/Integer;-><init>(I)V

    const/4 v8, 0x0

    aput-object v2, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v8

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f5b

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_23

    return-void

    :cond_23
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->p:Lcn/yonghui/hyd/coreui/widget/IconFont;

    invoke-virtual {v0}, Landroid/widget/TextView;->getBottom()I

    move-result v0

    new-instance v1, Lko/i;

    invoke-direct {v1, p0}, Lko/i;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    invoke-virtual {p0, p1, v0, v8, v1}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->setErrorView(IIILcn/yonghui/hyd/lib/style/widget/ErrorViewClickListener;)V

    return-void
.end method

.method public setLoading(Z)V
    .registers 10

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v4, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v3

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v0, 0x0

    const v5, 0x9f5a

    move-object v2, p0

    move-object v3, v4

    move v4, v0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_25

    return-void

    :cond_25
    invoke-virtual {p0, p1}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->showLoadingView(Z)V

    return-void
.end method

.method public showLoadingView(Z)V
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    new-instance v2, Ljava/lang/Byte;

    invoke-direct {v2, p1}, Ljava/lang/Byte;-><init>(B)V

    const/4 v8, 0x0

    aput-object v2, v1, v8

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v0, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    aput-object v0, v6, v8

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f42

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_23

    return-void

    :cond_23
    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->l1:Landroid/view/View;

    if-eqz p1, :cond_28

    goto :goto_2a

    :cond_28
    const/16 v8, 0x8

    :goto_2a
    invoke-virtual {v0, v8}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public t1(Ljava/lang/String;)V
    .registers 11

    const/4 v0, 0x1

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    const-class v0, Ljava/lang/String;

    aput-object v0, v6, v2

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f79

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_1e

    return-void

    :cond_1e
    sget-object v1, Ly8/c;->a:Ly8/c;

    invoke-virtual {p0}, Landroidx/fragment/app/b;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    move-result-object v2

    const/4 v4, 0x0

    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseAnalyticsActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    const v3, 0x7f1207be

    invoke-virtual {v0, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object v5

    const/4 v6, 0x0

    new-instance v7, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$k;

    invoke-direct {v7, p0}, Lcn/yonghui/hyd/order/detail/OrderDetailActivity$k;-><init>(Lcn/yonghui/hyd/order/detail/OrderDetailActivity;)V

    const/4 v8, 0x0

    move-object v3, p1

    invoke-virtual/range {v1 .. v8}, Ly8/c;->j(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLcn/yonghui/hyd/common/dialog/FeedBackDialogFragment$a;Lcn/yonghui/hyd/common/dialog/FeedBackDialogFragment$c;)V

    return-void
.end method

.method public updateSkinUI()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f50

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    invoke-super {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHTitleActivity;->updateSkinUI()V

    iget-object v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->y:Landroid/view/View;

    sget-object v1, Lcn/yonghui/hyd/appframe/theme/SkinUtils;->INSTANCE:Lcn/yonghui/hyd/appframe/theme/SkinUtils;

    const v2, 0x7f06023d

    invoke-virtual {v1, p0, v2}, Lcn/yonghui/hyd/appframe/theme/SkinUtils;->getColor(Landroid/content/Context;I)I

    move-result v3

    invoke-virtual {v0, v3}, Landroid/view/View;->setBackgroundColor(I)V

    invoke-virtual {v1, p0, v2}, Lcn/yonghui/hyd/appframe/theme/SkinUtils;->getColor(Landroid/content/Context;I)I

    move-result v0

    invoke-virtual {p0, v0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->setErrorViewBackground(I)V

    return-void
.end method

.method public x()V
    .registers 9

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    sget-object v3, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->changeQuickRedirect:Lcom/meituan/robust/ChangeQuickRedirect;

    new-array v6, v0, [Ljava/lang/Class;

    sget-object v7, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    const/4 v4, 0x0

    const v5, 0x9f5e

    move-object v2, p0

    invoke-static/range {v1 .. v7}, Lcom/meituan/robust/PatchProxy;->proxy([Ljava/lang/Object;Ljava/lang/Object;Lcom/meituan/robust/ChangeQuickRedirect;ZI[Ljava/lang/Class;Ljava/lang/Class;)Lcom/meituan/robust/PatchProxyResult;

    move-result-object v0

    iget-boolean v0, v0, Lcom/meituan/robust/PatchProxyResult;->isSupported:Z

    if-eqz v0, :cond_17

    return-void

    :cond_17
    invoke-virtual {p0}, Lcn/yonghui/hyd/lib/activity/BaseYHActivity;->removeErrorView()V

    return-void
.end method

.method public ya()V
    .registers 2

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcn/yonghui/hyd/order/detail/OrderDetailActivity;->M:Z

    return-void
.end method
