package dev.pigmomo.yhkit2025.viewmodel

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailProduct
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailPriceDetail
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailTimeSlot
import dev.pigmomo.yhkit2025.api.model.order.Button

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 订单详情ViewModel
 * 负责管理订单详情页面的数据和业务逻辑
 */
class OrderDetailViewModel(
    application: Application
) : AndroidViewModel(application) {

    @SuppressLint("StaticFieldLeak")
    private val context: Context = getApplication()

    // 订单详情数据
    private val _orderDetailData = MutableStateFlow<OrderDetailData?>(null)
    val orderDetailData: StateFlow<OrderDetailData?> = _orderDetailData.asStateFlow()

    // 加载状态
    private val _isLoading = mutableStateOf(false)
    val isLoading: State<Boolean> = _isLoading

    // 错误消息
    private val _errorMessage = mutableStateOf<String?>(null)
    val errorMessage: State<String?> = _errorMessage

    // Toast消息
    private val _toastMessage = MutableStateFlow<String?>(null)
    val toastMessage: StateFlow<String?> = _toastMessage.asStateFlow()

    /**
     * 设置订单详情数据
     * @param orderDetail 订单详情数据
     */
    fun setOrderDetailData(orderDetail: OrderDetailData) {
        _orderDetailData.value = orderDetail
        _errorMessage.value = null
        Log.d("OrderDetailViewModel", "Order detail data set: ${orderDetail.baseinfo?.id}")
    }

    /**
     * 获取订单状态文本
     */
    fun getOrderStatusText(): String {
        return _orderDetailData.value?.statusmsg ?: "未知状态"
    }

    /**
     * 获取订单状态描述
     */
    fun getOrderStatusDescription(): String {
        val orderDetail = _orderDetailData.value ?: return ""
        return when (orderDetail.status) {
            0 -> "您的订单超时未支付"
            1 -> "订单已确认，正在准备中"
            2 -> "订单正在配送中"
            3 -> "订单已完成"
            4 -> "订单已取消"
            else -> ""
        }
    }

    /**
     * 获取店铺名称
     */
    fun getShopName(): String {
        return _orderDetailData.value?.shopinfo?.seller?.title ?: "未知店铺"
    }

    /**
     * 获取商品列表
     */
    fun getProductList(): List<OrderDetailProduct> {
        return _orderDetailData.value?.productsinfo?.products ?: emptyList()
    }

    /**
     * 获取价格详情列表
     */
    fun getPriceDetailList(): List<OrderDetailPriceDetail> {
        return _orderDetailData.value?.pricedetail ?: emptyList()
    }

    /**
     * 获取实付金额
     */
    fun getTotalPayment(): String {
        val totalPayment = _orderDetailData.value?.orderpayinfo?.totalpayment ?: 0
        return formatPrice(totalPayment)
    }

    /**
     * 获取总优惠金额
     */
    fun getTotalDiscount(): String {
        val totalDiscount = _orderDetailData.value?.orderpayinfo?.totaldiscount ?: 0
        return formatPrice(totalDiscount)
    }

    /**
     * 获取订单编号
     */
    fun getOrderId(): String {
        return _orderDetailData.value?.baseinfo?.id ?: ""
    }

    /**
     * 获取下单时间
     */
    fun getOrderTime(): String {
        val generateTime = _orderDetailData.value?.baseinfo?.generateTime ?: 0
        return if (generateTime > 0) {
            formatTimestamp(generateTime)
        } else {
            "未知时间"
        }
    }

    /**
     * 获取支付方式
     */
    fun getPaymentMethod(): String {
        return _orderDetailData.value?.baseinfo?.paytypes ?: "未支付"
    }

    /**
     * 获取收货地址
     */
    fun getDeliveryAddress(): String {
        val recvInfo = _orderDetailData.value?.commentinfo?.recvinfo
        return if (recvInfo != null) {
            val area = recvInfo.address?.area ?: ""
            val detail = recvInfo.address?.detail ?: ""
            "$area$detail"
        } else {
            "未知地址"
        }
    }

    /**
     * 获取收货人信息
     */
    fun getReceiverInfo(): String {
        val recvInfo = _orderDetailData.value?.commentinfo?.recvinfo
        return if (recvInfo != null) {
            "${recvInfo.name} ${recvInfo.phone}"
        } else {
            "未知收货人"
        }
    }

    /**
     * 获取预约时间
     */
    fun getExpectedTime(): String {
        return _orderDetailData.value?.commentinfo?.expecttime ?: "尽快送达"
    }

    /**
     * 获取时间轴数据
     */
    fun getTimeslots(): List<OrderDetailTimeSlot> {
        return _orderDetailData.value?.timeslots?.data ?: emptyList()
    }

    /**
     * 获取订单备注
     */
    fun getOrderComment(): String {
        return _orderDetailData.value?.commentinfo?.comment ?: ""
    }

    /**
     * 获取缺货信息
     */
    fun getOutOfStockMessage(): String {
        return _orderDetailData.value?.baseinfo?.outOfStockMsg ?: ""
    }

    /**
     * 获取状态按钮列表
     */
    fun getStatusButtons(): List<Button> {
        return _orderDetailData.value?.statusinfo?.buttons ?: emptyList()
    }

    /**
     * 删除订单
     */
    fun deleteOrder() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 实现删除订单的API调用
                showToast("删除订单功能待实现")
            } catch (e: Exception) {
                Log.e("OrderDetailViewModel", "Delete order failed", e)
                showToast("删除订单失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 再来一单
     */
    fun reorder() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 实现再来一单的逻辑，将商品添加到购物车
                showToast("再来一单功能待实现")
            } catch (e: Exception) {
                Log.e("OrderDetailViewModel", "Reorder failed", e)
                showToast("再来一单失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 联系客服
     */
    fun contactCustomerService() {
        showToast("联系客服功能待实现")
    }

    /**
     * 查看物流信息
     */
    fun viewDeliveryInfo() {
        showToast("查看物流信息功能待实现")
    }

    /**
     * 显示Toast消息
     */
    fun showToast(message: String) {
        viewModelScope.launch {
            _toastMessage.value = message
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 清除Toast消息
     */
    fun clearToastMessage() {
        _toastMessage.value = null
    }

    /**
     * 格式化价格 (分转元)
     */
    @SuppressLint("DefaultLocale")
    private fun formatPrice(priceInCents: Int): String {
        return String.format("%.2f", priceInCents / 100.0)
    }

    /**
     * 格式化时间戳
     */
    private fun formatTimestamp(timestamp: Long): String {
        return try {
            val date = Date(timestamp * 1000) // 转换为毫秒
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(date)
        } catch (e: Exception) {
            "时间格式错误"
        }
    }

    /**
     * ViewModel工厂类
     */
    class Factory(
        private val application: Application
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(OrderDetailViewModel::class.java)) {
                return OrderDetailViewModel(application) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
