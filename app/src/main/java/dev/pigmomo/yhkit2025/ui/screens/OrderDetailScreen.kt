package dev.pigmomo.yhkit2025.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.rememberAsyncImagePainter
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay

/**
 * 订单详情屏幕
 * 显示订单的详细信息，包括状态、商品列表、价格明细等
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderDetailScreen(
    onBackClick: () -> Unit = {}
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("订单详情") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // 订单状态区域
            OrderStatusSection()
            
            // 店铺信息区域
            ShopInfoSection()
            
            // 商品列表区域
            ProductListSection()
            
            // 订单金额信息区域
            OrderPriceSection()
            
            // 订单信息区域
            OrderInfoSection()
            
            // 底部按钮区域
            BottomButtonsSection()
        }
    }
}

/**
 * 订单状态区域
 */
@Composable
fun OrderStatusSection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 8.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "订单已取消",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "查看详情"
                )
            }
            
            Text(
                text = "您的订单超时未支付",
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

/**
 * 店铺信息区域
 */
@Composable
private fun ShopInfoSection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "重庆石桥铺站",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.weight(1f))
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "查看店铺"
            )
        }
    }
}

/**
 * 商品列表区域
 */
@Composable
private fun ProductListSection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 商品1
            ProductItem(
                imageUrl = "",
                title = "【平价量贩】鲜猪五花肉 500g",
                price = "9.9",
                quantity = "1盒",
                actualPrice = "9.33"
            )
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // 商品2
            ProductItem(
                imageUrl = "",
                title = "正大初生鸡蛋 20枚",
                price = "9.9",
                quantity = "1盒",
                actualPrice = "9.33"
            )
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // 商品3
            ProductItem(
                imageUrl = "",
                title = "饭巢 川味辣卤料 200g",
                price = "16.5",
                quantity = "1包",
                actualPrice = "15.55"
            )
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // 显示"还有1种商品"
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "还有1种商品",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "查看更多",
                    tint = Color.Gray
                )
            }
        }
    }
}

/**
 * 商品项组件
 */
@Composable
private fun ProductItem(
    imageUrl: String,
    title: String,
    price: String,
    quantity: String,
    actualPrice: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 商品图片
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color.LightGray)
        ) {
            if (imageUrl.isNotEmpty()) {
                Image(
                    painter = rememberAsyncImagePainter(imageUrl),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // 使用默认图标
                Icon(
                    painter = painterResource(id = R.drawable.baseline_egg_alt_24),
                    contentDescription = null,
                    modifier = Modifier
                        .size(30.dp)
                        .align(Alignment.Center)
                )
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            // 商品标题
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 商品数量和价格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "单价: ¥$price/盒",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                Text(
                    text = "数量: $quantity",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 实付价格
        Column(horizontalAlignment = Alignment.End) {
            Text(
                text = "实付: ¥$actualPrice",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "¥$price",
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 订单金额信息区域
 */
@Composable
private fun OrderPriceSection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 商品金额
            PriceItem("商品金额", "¥52.2")
            
            // 包装服务费
            PriceItem("包装服务费", "¥1")
            
            // 红包
            PriceItem("红包", "-¥3", Color.Red)
            
            // 配送费
            PriceItem("配送费", "¥0", strikethrough = "¥6")
            
            Divider(modifier = Modifier.padding(vertical = 8.dp))
            
            // 实付金额
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "已优惠",
                        fontSize = 14.sp
                    )
                    Text(
                        text = "¥3",
                        fontSize = 14.sp,
                        color = Color.Red,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
                
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "实付",
                        fontSize = 14.sp
                    )
                    Text(
                        text = "¥50.2",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
            }
        }
    }
}

/**
 * 价格项组件
 */
@Composable
private fun PriceItem(
    label: String,
    value: String,
    valueColor: Color = Color.Black,
    strikethrough: String = ""
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color.Gray
        )
        
        Row(verticalAlignment = Alignment.CenterVertically) {
            if (strikethrough.isNotEmpty()) {
                Text(
                    text = strikethrough,
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(end = 4.dp)
                )
            }
            Text(
                text = value,
                fontSize = 14.sp,
                color = valueColor
            )
        }
    }
}

/**
 * 订单信息区域
 */
@Composable
private fun OrderInfoSection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 预约时间
            InfoItem("预约时间", "尽快送达（预计20:37前）")
            
            // 收货地址
            InfoItem("收货地址", "重庆市沙坪坝区石桥铺街道某某路某某号")
            
            // 收货人
            InfoItem("收货人", "张三 138****1234")
            
            // 订单编号
            InfoItem("订单编号", "1234567890123456")
            
            // 下单时间
            InfoItem("下单时间", "2023-06-01 18:30:45")
            
            // 支付方式
            InfoItem("支付方式", "未支付")
        }
    }
}

/**
 * 信息项组件
 */
@Composable
private fun InfoItem(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color.Gray,
            modifier = Modifier.width(80.dp)
        )
        
        Text(
            text = value,
            fontSize = 14.sp,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 底部按钮区域
 */
@Composable
private fun BottomButtonsSection() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.End
    ) {
        Button(
            onClick = { },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.White,
                contentColor = Color.Black
            ),
            modifier = Modifier
                .padding(end = 8.dp)
                .height(40.dp),
            shape = RoundedCornerShape(20.dp)
        ) {
            Text("删除订单")
        }
        
        Button(
            onClick = { },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.White,
                contentColor = Color.Black
            ),
            modifier = Modifier
                .padding(end = 8.dp)
                .height(40.dp),
            shape = RoundedCornerShape(20.dp)
        ) {
            Text("再来一单")
        }
    }
} 