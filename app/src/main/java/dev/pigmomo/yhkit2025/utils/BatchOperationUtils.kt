package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.order.Order
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.utils.AddressUtils.matchAndAssignParamsByXYHBizParamsCommon
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.viewmodel.OrderViewModel
import dev.pigmomo.yhkit2025.viewmodel.determineAccountType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

/**
 * 用于处理批量操作的工具类。
 *
 * 使用方法:
 * 在ViewModel中，调用 BatchOperationUtils.execute(...) 来开始批量操作。
 * ```
 * fun runBatchAccountOperations(operations: Set<String>, multiThread: Boolean) {
 *     BatchOperationUtils.execute(this, operations, BatchOperationUtils.OperationType.ACCOUNT, multiThread)
 * }
 * ```
 */
object BatchOperationUtils {

    private const val TAG = "BatchOperationUtils"

    /**
     * 地址参数缓存管理器，用于存储固定地址的相关参数
     * 避免重复获取店铺信息
     */
    object AddressParamsCache {
        // 缓存结构：addressId -> 参数Map
        private val cache = ConcurrentHashMap<String, AddressParams>()

        /**
         * 地址相关参数数据类
         */
        data class AddressParams(
            val xyhBizParams: String,
            val webXyhBizParams: String,
            val shopId: String,
            val sellerId: String,
            val cityId: String,
            val district: String
        )

        /**
         * 检查是否存在缓存的地址参数
         * @param addressId 地址ID
         * @return 是否存在缓存
         */
        fun hasCache(addressId: String): Boolean {
            return cache.containsKey(addressId)
        }

        /**
         * 获取缓存的地址参数
         * @param addressId 地址ID
         * @return 地址参数，如果不存在则返回null
         */
        fun getParams(addressId: String): AddressParams? {
            return cache[addressId]
        }

        /**
         * 保存地址参数到缓存
         * @param addressId 地址ID
         * @param params 地址参数
         */
        fun saveParams(addressId: String, params: AddressParams) {
            cache[addressId] = params
        }

        /**
         * 清除所有缓存
         */
        fun clearCache() {
            cache.clear()
        }
    }

    /**
     * 定义操作的类型，分为账号操作和活动操作。
     */
    enum class OperationType {
        ACCOUNT,
        ACTIVITY
    }

    /**
     * 执行批量操作。
     * @param viewModel OrderViewModel的实例。
     * @param operations 一个包含要执行操作名称的集合。
     * @param type 操作类型 (ACCOUNT 或 ACTIVITY)。
     * @param multiThreadRangeList 多线程范围列表，指定要使用的orderTokens索引。
     * @param activityGetType 活动操作类型，发起(1)、参加(2)，默认为发起。
     * @param context 应用上下文，用于数据库操作，默认为null。
     */
    fun execute(
        viewModel: OrderViewModel,
        operations: Set<String>,
        type: OperationType,
        multiThreadRangeList: List<Int>,
        activityGetType: String = "1", // 默认为发起操作
        context: Context? = null,
        setCurrentOrderTokenIndex: (Int) -> Unit = {}
    ) {
        // 在主线程启动协程，以在操作完成后更新UI状态
        CoroutineScope(Dispatchers.Main).launch {
            try {
                // 收集所需参数
                val params = collectOperationParameters(viewModel, multiThreadRangeList)

                // 如果没有有效令牌，则提前结束
                if (params.tokensToProcess.isEmpty()) {
                    Log.e(TAG, "No valid tokens to process")
                    resetOperationState(viewModel, type)
                    return@launch
                }

                Log.d(
                    TAG,
                    "Starting batch operations for ${params.tokensToProcess.size} tokens from multiThreadRangeList: $multiThreadRangeList"
                )

                // 初始化进度记录
                val totalTokensCount = params.tokensToProcess.size
                OperationProgressUtils.initProgress(
                    when (type) {
                        OperationType.ACCOUNT -> OperationProgressUtils.OperationType.ACCOUNT
                        OperationType.ACTIVITY -> OperationProgressUtils.OperationType.ACTIVITY
                    },
                    operations, totalTokensCount
                )

                // 执行批量操作
                executeParallel(
                    viewModel,
                    params.tokensToProcess,
                    params.tokensToProcessIndexes,
                    operations,
                    type,
                    params.serviceType,
                    params.fastenAddressItem,
                    activityGetType,
                    params.boostCouponHelpData,
                    params.selectedProxy,
                    params.intervalTime,
                    context,
                    setCurrentOrderTokenIndex
                )

                // 重置ViewModel中的操作状态标记
                resetOperationState(viewModel, type)

            } catch (e: Exception) {
                Log.e(TAG, "Error executing batch operations", e)
                // 发生异常时也要完成进度记录
                resetOperationState(viewModel, type)
            }
        }
    }

    /**
     * 收集批量操作所需的参数
     */
    private fun collectOperationParameters(
        viewModel: OrderViewModel,
        multiThreadRangeList: List<Int>
    ): OperationParameters {
        val allTokens = viewModel.orderTokens.value
        val serviceType = viewModel.serviceType.value
        val fastenAddress = viewModel.fastenAddress.value
        val fastenAddressItem = if (fastenAddress) {
            viewModel.selectedAddressItem.value
        } else {
            null
        }
        val boostCouponHelpData = viewModel.boostCouponHelpData.value
        val intervalTime: Long = if (viewModel.enableProxy.value) 3000L else 5000L
        val selectedProxy = viewModel.selectedProxy.value

        // 根据multiThreadRangeList筛选要处理的令牌
        val tokensToProcess = mutableListOf<OrderTokenEntity>()
        val tokensToProcessIndexes = mutableListOf<Int>()

        for (rangeIndex in multiThreadRangeList) {
            // 索引从1开始，需要减1
            val tokenIndex = rangeIndex - 1

            if (tokenIndex >= 0 && tokenIndex < allTokens.size) {
                tokensToProcess.add(allTokens[tokenIndex])
                // 记录要处理的令牌序号
                tokensToProcessIndexes.add(tokenIndex + 1)
            } else {
                Log.e(TAG, "Invalid token index: $tokenIndex (from range index: $rangeIndex)")
            }
        }

        return OperationParameters(
            tokensToProcess,
            tokensToProcessIndexes,
            serviceType,
            fastenAddressItem,
            boostCouponHelpData,
            intervalTime,
            selectedProxy
        )
    }

    /**
     * 批量操作参数数据类
     */
    private data class OperationParameters(
        val tokensToProcess: List<OrderTokenEntity>,
        val tokensToProcessIndexes: List<Int>,
        val serviceType: String,
        val fastenAddressItem: AddressItem?,
        val boostCouponHelpData: List<String>,
        val intervalTime: Long,
        val selectedProxy: String
    )

    /**
     * 重置操作状态
     */
    private fun resetOperationState(viewModel: OrderViewModel, type: OperationType) {
        when (type) {
            OperationType.ACCOUNT -> {
                viewModel.setIsOrderTokensOperating(false)
            }

            OperationType.ACTIVITY -> {
                viewModel.setIsActivitiesOperating(false)
            }
        }
    }

    /**
     * 并行执行令牌操作
     */
    private suspend fun executeParallel(
        viewModel: OrderViewModel,
        tokens: List<OrderTokenEntity>,
        tokensToProcessIndexes: List<Int>,
        operations: Set<String>,
        type: OperationType,
        serviceType: String,
        fastenAddressItem: AddressItem?,
        activityGetType: String,
        boostCouponHelpData: List<String>,
        selectedProxy: String,
        intervalTime: Long,
        context: Context?,
        setCurrentOrderTokenIndex: (Int) -> Unit = {}
    ) {
        // 依次启动每个账号的操作，设置间隔时间
        val jobs = mutableListOf<Job>()

        var totalCount = 0

        // 使用一个计时器来确保线程真正错开执行
        for ((index, token) in tokens.withIndex()) {
            // 实时检查ViewModel中的状态，而不是使用传入的快照值
            val isOperating = when (type) {
                OperationType.ACCOUNT -> viewModel.isOrderTokensOperating.value
                OperationType.ACTIVITY -> viewModel.isActivitiesOperating.value
            }

            // 检查是否暂停，暂停结束循环，不执行后续操作
            if (!isOperating) {
                Log.d(TAG, "停止操作所有未进行的操作")
                // 记录失败的账号序号
                for (i in index until tokensToProcessIndexes.size) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        when (type) {
                            OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                            OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                        },
                        tokensToProcessIndexes[i],
                        type.name
                    )
                }
                OperationProgressUtils.updateTotalCount(totalCount)
                return
            }

            // 第一个账号立即执行，后续账号错开执行
            // 注意：这里的delay是在主协程上下文中执行的，会真正错开线程的启动时间
            if (index > 0) {
                Log.d(TAG, "Delaying start of ${index + 1}th token by $intervalTime ms")
                delay(intervalTime)

                // 延迟后再次检查状态，确保在延迟期间没有触发的暂停操作
                val isStillOperating = when (type) {
                    OperationType.ACCOUNT -> viewModel.isOrderTokensOperating.value
                    OperationType.ACTIVITY -> viewModel.isActivitiesOperating.value
                }

                if (!isStillOperating) {
                    Log.d(TAG, "在延迟期间检测到暂停信号，停止后续操作")
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        when (type) {
                            OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                            OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                        },
                        tokensToProcessIndexes[index],
                        type.name
                    )
                    continue
                }
            }

            setCurrentOrderTokenIndex(tokensToProcessIndexes[index])
            OperationProgressUtils.updateProgress()
            totalCount++

            Log.d(TAG, "Starting execution for token ${index + 1}: ${token.phoneNumber}")
            val job = CoroutineScope(Dispatchers.IO).launch {
                performOperationsForToken(
                    token,
                    tokensToProcessIndexes[index],
                    operations,
                    type,
                    serviceType,
                    fastenAddressItem,
                    activityGetType,
                    boostCouponHelpData,
                    selectedProxy,
                    context,
                    viewModel::updateTokenLogin
                )
            }
            jobs.add(job)
        }

        jobs.joinAll() // 等待所有任务完成
    }

    /**
     * 为单个账号执行所有指定的操作。
     */
    private suspend fun performOperationsForToken(
        token: OrderTokenEntity,
        tokenIndex: Int,
        operations: Set<String>,
        type: OperationType,
        serviceType: String,
        fastenAddressItem: AddressItem?,
        activityGetType: String,
        boostCouponHelpData: List<String>,
        selectedProxy: String,
        context: Context?,
        updateToken: (OrderTokenEntity) -> Unit
    ) {
        // 创建进度记录器
        val progressRecorder = ProcessRecorder(
            tokenUid = token.uid,
            phoneNumber = token.phoneNumber,
            tag = TAG,
            logLevelDefault = "INFO",
            saveToDb = true,
            context = context
        )

        // 添加变量跟踪操作成功状态，初始为true
        var operationSuccess = true

        try {
            Log.d(TAG, "Processing token: ${token.phoneNumber}")
            progressRecorder.recordProcess("批量处理账号: ${token.phoneNumber}")

            // 创建RequestService实例，而不是切换ViewModel上下文
            val requestService = RequestService.create(token, serviceType)

            // 启用代理
            if (selectedProxy.isNotEmpty()) {
                val proxyConfig =
                    HttpProxyUtils.getProxyIp(selectProxyConfig = selectedProxy)
                if (proxyConfig.proxyInfo.first != null && proxyConfig.proxyInfo.second != null) {
                    // 获取代理实际IP和端口
                    val actualProxyInfo = HttpProxyUtils.getProxyIpInfo(
                        proxyConfig.proxyInfo,
                        proxyConfig.proxyAccount
                    )
                    val realProxyIp = actualProxyInfo.ip

                    if (realProxyIp.isEmpty()) {
                        Log.e(TAG, "Failed to get actual proxy info")
                        progressRecorder.recordProcess("当前IP获取错误，停止执行操作", "ERROR")
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            when (type) {
                                OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                            },
                            tokenIndex,
                            type.name
                        )
                        // 设置操作失败
                        OperationProgressUtils.recordFailure()
                        return
                    } else {
                        progressRecorder.recordProcess("当前IP: $realProxyIp")
                    }

                    val success = runBlocking {
                        requestService.setupProxy(proxyConfig)
                    }
                    if (success) {
                        progressRecorder.recordProcess("代理设置成功: ${proxyConfig.proxyInfo.first}:${proxyConfig.proxyInfo.second}")
                    } else {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            when (type) {
                                OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                            },
                            tokenIndex,
                            type.name
                        )
                        progressRecorder.recordProcess("代理设置错误，停止执行操作", "ERROR")
                        // 设置操作失败
                        OperationProgressUtils.recordFailure()
                        return
                    }
                } else {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        when (type) {
                            OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                            OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                        },
                        tokenIndex,
                        type.name
                    )
                    progressRecorder.recordProcess("代理设置错误，停止执行操作", "ERROR")
                    // 设置操作失败
                    OperationProgressUtils.recordFailure()
                    return
                }
            } else {
                // 即使不使用代理，也检查当前IP
                val actualProxyInfo = HttpProxyUtils.getProxyIpInfo()
                val realProxyIp = actualProxyInfo.ip

                if (realProxyIp.isEmpty()) {
                    Log.e(TAG, "Failed to get IP info")
                    progressRecorder.recordProcess("未获取到当前IP，停止执行操作", "ERROR")
                    // 记录失败的账号序号
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        when (type) {
                            OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                            OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                        },
                        tokenIndex,
                        type.name
                    )
                    // 设置操作失败
                    OperationProgressUtils.recordFailure()
                    return
                } else {
                    progressRecorder.recordProcess("当前IP: $realProxyIp")
                }
            }

            // 初始化必要的参数
            var lsAddressList: List<AddressItem> = emptyList()
            var selectedAddress: AddressItem? = null
            var initialized = false

            // 检查是否有固定地址
            if (fastenAddressItem != null) {
                // 使用固定地址
                selectedAddress = fastenAddressItem
                lsAddressList = listOf(fastenAddressItem)
                progressRecorder.recordProcess("已选择固定地址: ${selectedAddress.address.area} ${selectedAddress.address.detail}")
            } else {
                // 获取地址列表
                progressRecorder.recordProcess("获取地址列表")
                val addressResult = runBlocking {
                    requestService.address.getAllAddress()
                }

                when (addressResult) {
                    is RequestResult.Success -> {
                        val addressResponse =
                            ResponseParserUtils.parseAddressResponse(addressResult.data)
                        if (addressResponse != null && addressResponse.code == 0) {
                            val addressList = addressResponse.data?.list ?: emptyList()

                            if (addressList.isNotEmpty()) {
                                // 全部地址
                                lsAddressList = addressList
                                // 选择第一个地址
                                selectedAddress = addressList[0]
                                progressRecorder.recordProcess("已选择地址: ${selectedAddress.address.area} ${selectedAddress.address.detail}")
                            } else {
                                progressRecorder.recordProcess("地址列表为空", "WARNING")
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    when (type) {
                                        OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                        OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                                    },
                                    tokenIndex,
                                    type.name
                                )
                                OperationProgressUtils.recordFailure()
                                return
                            }
                        } else {
                            progressRecorder.recordProcess(
                                "获取地址列表错误: ${addressResponse?.message ?: "未知错误"}",
                                "ERROR"
                            )
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                when (type) {
                                    OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                    OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                                },
                                tokenIndex,
                                type.name
                            )
                            OperationProgressUtils.recordFailure()
                            return
                        }
                    }

                    is RequestResult.Error -> {
                        progressRecorder.recordProcess(
                            "获取地址列表错误: ${addressResult.error.message}",
                            "ERROR"
                        )
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            when (type) {
                                OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                            },
                            tokenIndex,
                            type.name
                        )
                        OperationProgressUtils.recordFailure()
                        return
                    }
                }
            }

            // 检查是否存在固定地址的缓存参数
            if (fastenAddressItem != null && AddressParamsCache.hasCache(selectedAddress.id)) {
                // 使用缓存的参数
                val cachedParams = AddressParamsCache.getParams(selectedAddress.id)
                if (cachedParams != null) {
                    progressRecorder.recordProcess("使用固定地址缓存参数")

                    // 设置缓存的参数
                    requestService.setXyhBizParams(cachedParams.xyhBizParams)
                    requestService.setWebXyhBizParams(cachedParams.webXyhBizParams)
                    requestService.setShopId(cachedParams.shopId)
                    requestService.setSellerId(cachedParams.sellerId)
                    requestService.setCityId(cachedParams.cityId)
                    requestService.setDistrict(cachedParams.district)

                    initialized = true
                }
            } else {
                // 获取店铺信息
                val shopResult = runBlocking {
                    requestService.shop.getFbShopLbs(selectedAddress)
                }

                when (shopResult) {
                    is RequestResult.Success -> {
                        val shopResponse =
                            ResponseParserUtils.parseShopResponse(shopResult.data)
                        if (shopResponse != null && shopResponse.code == 0) {
                            val addressInfo = shopResponse.data?.address?.firstOrNull()
                            val cityInfo = shopResponse.data?.city
                            val shopInfoList = shopResponse.data?.seller

                            if (addressInfo != null && cityInfo != null && !shopInfoList.isNullOrEmpty()) {
                                val sellerId = shopInfoList[0].sellerid.toString()
                                val shopId = shopInfoList[0].shopid

                                // 获取XYH业务参数
                                val addressId = addressInfo.id
                                val lat = addressInfo.location?.lat ?: ""
                                val lng = addressInfo.location?.lng ?: ""
                                val cityId = cityInfo.id
                                val district = addressInfo.address.district

                                val xyhResult = runBlocking {
                                    requestService.getXyhBizParams(
                                        lat = lat,
                                        lng = lng,
                                        cityid = cityId,
                                        district = district,
                                        sellerid = sellerId,
                                        shopid = shopId,
                                        addressId = addressId,
                                        serviceType = serviceType
                                    )
                                }

                                when (xyhResult) {
                                    is RequestResult.Success -> {
                                        // 设置XYH业务参数
                                        val xyhBizParams = xyhResult.data
                                        requestService.setXyhBizParams(xyhBizParams)
                                        val webXyhBizParams =
                                            matchAndAssignParamsByXYHBizParamsCommon(
                                                xyhBizParams
                                            )
                                        requestService.setWebXyhBizParams(
                                            webXyhBizParams
                                        )

                                        // 设置店铺ID和销售商ID
                                        requestService.setShopId(shopId)
                                        requestService.setSellerId(sellerId)
                                        requestService.setCityId(cityId)
                                        requestService.setDistrict(district)

                                        // 如果是固定地址，缓存参数
                                        if (fastenAddressItem != null) {
                                            val params = AddressParamsCache.AddressParams(
                                                xyhBizParams = xyhBizParams,
                                                webXyhBizParams = webXyhBizParams,
                                                shopId = shopId,
                                                sellerId = sellerId,
                                                cityId = cityId,
                                                district = district
                                            )
                                            AddressParamsCache.saveParams(
                                                selectedAddress.id,
                                                params
                                            )
                                            progressRecorder.recordProcess("固定地址参数已缓存")
                                        }

                                        progressRecorder.recordProcess("XYH参数设置成功")
                                        initialized = true
                                    }

                                    is RequestResult.Error -> {
                                        progressRecorder.recordProcess(
                                            "获取XYH业务参数错误: ${xyhResult.error.message}",
                                            "ERROR"
                                        )
                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                            when (type) {
                                                OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                                OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                                            },
                                            tokenIndex,
                                            type.name
                                        )
                                        OperationProgressUtils.recordFailure()
                                        return
                                    }
                                }
                            } else {
                                progressRecorder.recordProcess("店铺数据不完整", "WARNING")
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    when (type) {
                                        OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                        OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                                    },
                                    tokenIndex,
                                    type.name
                                )
                                OperationProgressUtils.recordFailure()
                                return
                            }
                        } else {
                            progressRecorder.recordProcess(
                                "获取店铺信息错误: ${shopResponse?.message ?: "未知错误"}",
                                "ERROR"
                            )
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                when (type) {
                                    OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                    OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                                },
                                tokenIndex,
                                type.name
                            )
                            OperationProgressUtils.recordFailure()
                            return
                        }
                    }

                    is RequestResult.Error -> {
                        progressRecorder.recordProcess(
                            "获取店铺信息错误: ${shopResult.error.message}",
                            "ERROR"
                        )
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            when (type) {
                                OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                                OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                            },
                            tokenIndex,
                            type.name
                        )
                        OperationProgressUtils.recordFailure()
                        return
                    }
                }
            }

            // 只有在初始化成功后才执行操作
            if (initialized) {
                // 获取初始token，后续操作将在此基础上更新
                var currentToken = requestService.getToken() as? OrderTokenEntity
                if (currentToken == null) {
                    progressRecorder.recordProcess("OrderTokenEntity初始化错误", "ERROR")
                    // 设置操作失败
                    OperationProgressUtils.recordFailure()
                    return
                }
                for (operation in operations) {
                    Log.d(TAG, "Executing operation '$operation' for token: ${token.phoneNumber}")
                    progressRecorder.recordProcess("执行操作: $operation")

                    when (type) {
                        OperationType.ACCOUNT -> {
                            val result = performAccountOperation(
                                operation,
                                progressRecorder,
                                requestService,
                                selectedAddress,
                                lsAddressList,
                                tokenIndex,
                                currentToken!!,
                                // 传递操作成功状态的引用
                                { success -> operationSuccess = operationSuccess && success }
                            )
                            // 如果返回了更新的token，则更新我们的本地副本
                            if (result != null) {
                                currentToken = result
                            }
                        }

                        OperationType.ACTIVITY -> {
                            val success = performActivityOperation(
                                operation,
                                progressRecorder,
                                requestService,
                                selectedAddress,
                                activityGetType,
                                boostCouponHelpData,
                                context,
                                token,
                                tokenIndex
                            )
                            // 更新操作成功状态
                            operationSuccess = operationSuccess && success
                        }
                    }
                }

                // 在所有操作完成后，一次性更新token
                if (type == OperationType.ACCOUNT) {
                    currentToken?.let { updateToken(it) }
                }
            } else {
                progressRecorder.recordProcess("初始化错误，停止执行操作", "ERROR")
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    when (type) {
                        OperationType.ACCOUNT -> FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION
                        OperationType.ACTIVITY -> FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION
                    },
                    tokenIndex,
                    type.name
                )
                // 设置操作失败
                operationSuccess = false
            }

            Log.d(TAG, "Finished all operations for token: ${token.phoneNumber}")

            delay(200)

            // 在所有操作完成后，根据操作成功状态记录一次成功或失败
            if (operationSuccess) {
                OperationProgressUtils.recordSuccess()
                progressRecorder.recordProcess("所有操作执行成功")
            } else {
                OperationProgressUtils.recordFailure()
                progressRecorder.recordProcess("部分或全部操作执行错误", "WARNING")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing token ${token.phoneNumber}", e)
            progressRecorder.recordProcess("处理出错: ${e.message}", "ERROR")
            OperationProgressUtils.recordFailure()
        }
    }

    /**
     * 执行具体的账号操作。
     * @param operation 操作名称。
     * @param progressRecorder 进度记录器。
     * @param requestService RequestService实例。
     * @param selectedAddress 选中的地址
     * @param lsAddressList 非固定地址列表，用于清空地址
     * @param updateOperationStatus 更新操作状态的回调函数
     * @return 更新后的OrderTokenEntity，如果没有更新则返回null
     */
    private fun performAccountOperation(
        operation: String,
        progressRecorder: ProcessRecorder,
        requestService: RequestService,
        selectedAddress: AddressItem,
        lsAddressList: List<AddressItem>,
        tokenIndex: Int = 0, // 添加账号序号参数
        currentToken: OrderTokenEntity,
        updateOperationStatus: (Boolean) -> Unit
    ): OrderTokenEntity? {
        var updatedToken = currentToken
        var operationSuccess = true

        try {
            when (operation) {
                "查询状态" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过查询状态", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    updatedToken = handleQueryInfoOperation(
                        requestService,
                        progressRecorder,
                        tokenIndex,
                        currentToken,
                        { success -> operationSuccess = success }
                    ) ?: updatedToken
                }

                "查询余额" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过查询余额", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    updatedToken = handleQueryBalanceOperation(
                        requestService,
                        progressRecorder,
                        tokenIndex,
                        currentToken,
                        { success -> operationSuccess = success }
                    ) ?: updatedToken
                }

                "查询积分" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过查询积分", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    updatedToken = handleQueryPointOperation(
                        requestService,
                        progressRecorder,
                        tokenIndex,
                        currentToken,
                        { success -> operationSuccess = success }
                    ) ?: updatedToken
                }

                "查询订单" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过查询订单", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    operationSuccess = handleQueryOrderOperation(
                        requestService,
                        progressRecorder,
                        tokenIndex
                    )
                }

                "清空地址" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过清空地址", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    if (lsAddressList.isEmpty()) {
                        progressRecorder.recordProcess("地址列表为空，停止清空")
                        // 不在这里记录成功
                    } else {
                        progressRecorder.recordProcess("开始清空地址，共${lsAddressList.size}个")
                        operationSuccess = handleClearAddressOperation(
                            lsAddressList,
                            requestService,
                            progressRecorder,
                            tokenIndex
                        )
                    }
                }

                "清空购物车" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过清空购物车", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    operationSuccess = handleClearCartOperation(
                        requestService,
                        progressRecorder,
                        selectedAddress,
                        tokenIndex
                    )
                }

                "清空订单" -> {
                    if (!updatedToken.isLogin) {
                        progressRecorder.recordProcess("账号未登录，跳过清空订单", "WARNING")
                        updateOperationStatus(false)
                        return updatedToken
                    }
                    operationSuccess = handleClearOrderOperation(
                        requestService,
                        progressRecorder,
                        tokenIndex
                    )
                }

                else -> {
                    Log.w(TAG, "Unknown account operation: $operation")
                    progressRecorder.recordProcess("未知的账号操作: $operation", "ERROR")
                    operationSuccess = false
                }
            }

            // 更新操作状态
            updateOperationStatus(operationSuccess)
            return updatedToken
        } catch (e: Exception) {
            Log.e(TAG, "Error executing account operation: $operation", e)
            progressRecorder.recordProcess("账号操作错误: $operation ${e.message}", "ERROR")
            // 更新操作状态为失败
            updateOperationStatus(false)
            return null
        }
    }

    /**
     * 执行具体的活动操作。
     * @param operation 操作名称。
     * @param progressRecorder 进度记录器。
     * @param requestService RequestService实例。
     * @param selectedAddress 选中的地址
     * @param activityGetType 活动操作类型，发起(1)、参加(2)
     * @return 操作是否成功
     */
    private fun performActivityOperation(
        operation: String,
        progressRecorder: ProcessRecorder,
        requestService: RequestService,
        selectedAddress: AddressItem,
        activityGetType: String,
        boostCouponHelpData: List<String>,
        context: Context?,
        token: OrderTokenEntity,
        tokenIndex: Int
    ): Boolean {
        var operationSuccess = true

        try {
            when (operation) {
                "积分组队" -> {
                    // 根据活动操作类型执行对应操作
                    when (activityGetType) {
                        "1" -> {
                            // 发起操作
                            operationSuccess = handlePointTeamOperation(
                                requestService,
                                progressRecorder,
                                tokenIndex
                            )
                        }

                        "2" -> {
                            // 参加操作
                            progressRecorder.recordProcess("参加他人的积分组队需要团队代码")
                            operationSuccess = false
                        }

                        else -> {
                            progressRecorder.recordProcess(
                                "未知的积分组队操作类型: $activityGetType",
                                "ERROR"
                            )
                            operationSuccess = false
                        }
                    }
                }

                "助力券" -> {
                    when (activityGetType) {
                        "1" -> {
                            // 检查助力券数据是否为空
                            if (boostCouponHelpData.isEmpty()) {
                                progressRecorder.recordProcess(
                                    "助力券配置数据为空，无法发起助力",
                                    "WARNING"
                                )
                                operationSuccess = false
                            } else {
                                operationSuccess = handleBoostCouponOperation(
                                    boostCouponHelpData,
                                    requestService,
                                    progressRecorder,
                                    context,
                                    token,
                                    tokenIndex
                                )
                            }
                        }

                        "2" -> {
                            // 参加助力券活动
                            operationSuccess = handleJoinBoostCouponOperation(
                                requestService,
                                progressRecorder,
                                context,
                                token,
                                tokenIndex
                            )
                        }

                        else -> {
                            progressRecorder.recordProcess(
                                "未知的助力券操作类型: $activityGetType",
                                "ERROR"
                            )
                            operationSuccess = false
                        }
                    }
                }

                "邀请有礼" -> {
                    when (activityGetType) {
                        "1" -> {
                            // 发起邀请有礼
                            operationSuccess = handleInvitationOperation(
                                requestService,
                                progressRecorder,
                                tokenIndex
                            )
                        }

                        "2" -> {
                            // 参加邀请有礼
                            progressRecorder.recordProcess("参加他人的邀请有礼需要邀请码")
                            operationSuccess = false
                        }

                        else -> {
                            progressRecorder.recordProcess(
                                "未知的邀请有礼操作类型: $activityGetType",
                                "ERROR"
                            )
                            operationSuccess = false
                        }
                    }
                }

                else -> {
                    Log.w(TAG, "Unknown activity operation: $operation")
                    progressRecorder.recordProcess("未知的活动操作: $operation", "ERROR")
                    operationSuccess = false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing activity operation: $operation", e)
            progressRecorder.recordProcess("活动操作错误: $operation ${e.message}", "ERROR")
            operationSuccess = false
        }

        return operationSuccess
    }

    private fun handlePointTeamOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        val result = runBlocking {
            requestService.credit.pointTeam()
        }

        when (result) {
            is RequestResult.Success -> {
                val response = ResponseParserUtils.parseDividePointTeamResponse(result.data)
                if (response != null && response.code == 0) {
                    val teamCode = response.data?.teamCode ?: ""
                    if (teamCode.isNotEmpty()) {
                        progressRecorder.recordProcess("积分组队: teamCode=$teamCode")
                        return true
                    } else {
                        progressRecorder.recordProcess("未获取到积分组队", "WARNING")
                        return false
                    }
                } else {
                    progressRecorder.recordProcess(
                        "发起积分组队错误: ${response?.message ?: "未知错误"}",
                        "ERROR"
                    )
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                            tokenIndex,
                            "积分组队"
                        )
                    }
                    return false
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess("发起积分组队异常: ${result.error.message}", "ERROR")
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                        tokenIndex,
                        "积分组队"
                    )
                }
                return false
            }
        }
    }

    private fun handleBoostCouponOperation(
        boostCouponHelpData: List<String>,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        context: Context?,
        token: OrderTokenEntity,
        tokenIndex: Int
    ): Boolean {
        var successCount = 0
        var failCount = 0

        // 遍历所有助力券ID，获取游戏码并发起助力
        for (prizeId in boostCouponHelpData) {
            try {
                progressRecorder.recordProcess("发起助力券: prizeId=$prizeId")

                // 获取游戏码
                val gameCodeResult = runBlocking {
                    requestService.coupon.getGameCode(prizeId)
                }

                when (gameCodeResult) {
                    is RequestResult.Success -> {
                        val response =
                            ResponseParserUtils.parseBoostCouponGameCodeResponse(gameCodeResult.data)

                        if (response != null && response.code == 0) {
                            val gameCode = response.data
                            if (gameCode.isNotEmpty()) {
                                successCount++
                                progressRecorder.recordProcess("助力券参数: prizeId=$prizeId&gameCode=$gameCode")

                                // 保存游戏码到文件
                                context?.let { ctx ->
                                    // 检查游戏码是否已存在，避免重复保存
                                    if (GameCodeManager.isGameCodeExists(ctx, prizeId, gameCode)) {
                                        progressRecorder.recordProcess("助力券参数已存在，跳过保存: prizeId=$prizeId&gameCode=$gameCode")
                                    } else {
                                        val saved = GameCodeManager.saveGameCode(
                                            ctx,
                                            prizeId,
                                            gameCode,
                                            token.phoneNumber
                                        )
                                        if (saved) {
                                            progressRecorder.recordProcess(
                                                "助力券参数已保存到文件: ${
                                                    GameCodeManager.getGameCodeFilePath(
                                                        ctx
                                                    )
                                                }"
                                            )
                                            // 成功保存，但不在这里记录成功
                                        } else {
                                            progressRecorder.recordProcess(
                                                "助力券参数保存到文件错误: prizeId=$prizeId&gameCode=$gameCode",
                                                "WARNING"
                                            )
                                            // 保存失败，但不在这里记录失败
                                        }
                                    }
                                }
                            } else {
                                failCount++
                                progressRecorder.recordProcess(
                                    "助力券 $prizeId 助力券参数为空",
                                    "WARNING"
                                )
                                if (tokenIndex > 0) {
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                        tokenIndex,
                                        "助力券"
                                    )
                                }
                            }
                        } else {
                            failCount++
                            progressRecorder.recordProcess(
                                "助力券 $prizeId 获取助力券参数错误: ${response?.message ?: "未知错误"}",
                                "ERROR"
                            )
                            if (tokenIndex > 0 && response?.message?.contains("上限") == false) {
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                    tokenIndex,
                                    "助力券"
                                )
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        failCount++
                        progressRecorder.recordProcess(
                            "助力券 $prizeId 获取助力券参数请求错误: ${gameCodeResult.error.message}",
                            "ERROR"
                        )
                        if (tokenIndex > 0) {
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                tokenIndex,
                                "助力券"
                            )
                        }
                        Log.e(
                            TAG,
                            "Error getting game code for prize: $prizeId",
                            gameCodeResult.error
                        )
                    }
                }

                // 添加间隔，避免请求过快
                runBlocking {
                    delay(1000)
                }

            } catch (e: Exception) {
                failCount++
                progressRecorder.recordProcess(
                    "处理助力券 $prizeId 时发生异常: ${e.message}",
                    "ERROR"
                )
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                        tokenIndex,
                        "助力券"
                    )
                }
                Log.e(TAG, "Exception processing boost coupon: $prizeId", e)
            }
        }

        // 记录最终结果
        progressRecorder.recordProcess("助力券发起完成: 成功${successCount}个, 失败${failCount}个")
        return successCount > 0
    }

    private fun handleInvitationOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        try {
            // 调用API获取邀请活动信息
            val result = runBlocking {
                requestService.invitation.invitationV2ActivityInfo()
            }

            when (result) {
                is RequestResult.Success -> {
                    val response =
                        ResponseParserUtils.parseInvitationV2ActiveInfoResponse(result.data)
                    if (response != null && response.code == 0) {
                        val data = response.data
                        if (data != null && data.baseinfo != null) {
                            // 构造邀请码
                            val invitationCode =
                                "${data.memberId},${data.baseinfo.activitycode},${data.shopId}"

                            // 记录邀请码，但不复制到剪贴板
                            progressRecorder.recordProcess("邀请码: $invitationCode")
                            return true
                        } else {
                            progressRecorder.recordProcess(
                                "获取邀请活动信息错误: 数据为空",
                                "ERROR"
                            )
                            if (tokenIndex > 0) {
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                    tokenIndex,
                                    "邀请有礼"
                                )
                            }
                            return false
                        }
                    } else {
                        progressRecorder.recordProcess(
                            "获取邀请活动信息错误: ${response?.message ?: "未知错误"}",
                            "ERROR"
                        )
                        if (tokenIndex > 0) {
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                tokenIndex,
                                "邀请有礼"
                            )
                        }
                        return false
                    }
                }

                is RequestResult.Error -> {
                    progressRecorder.recordProcess(
                        "获取邀请活动信息错误: ${result.error.message}",
                        "ERROR"
                    )
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                            tokenIndex,
                            "邀请有礼"
                        )
                    }
                    Log.e(TAG, "Error getting invitation activity info", result.error)
                    return false
                }
            }
        } catch (e: Exception) {
            progressRecorder.recordProcess("获取邀请码过程中发生异常: ${e.message}", "ERROR")
            Log.e(TAG, "Exception while getting invitation code", e)
            if (tokenIndex > 0) {
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                    tokenIndex,
                    "邀请有礼"
                )
            }
            return false
        }
    }

    private fun handleJoinBoostCouponOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        context: Context?,
        token: OrderTokenEntity,
        tokenIndex: Int
    ): Boolean {
        if (context == null) {
            progressRecorder.recordProcess("Context为空，停止参加助力券操作", "ERROR")
            return false
        }

        progressRecorder.recordProcess("开始参加助力券操作，从文件读取gameCode")

        var successCount = 0
        var failCount = 0

        // 添加重试机制
        var retryCount = 0
        var gameCodeInfoList = emptyList<GameCodeManager.GameCodeInfo>()

        while (gameCodeInfoList.isEmpty() && retryCount < 3) {
            // 原子操作：获取并标记多个可用的gameCode，每个prizeId一个
            gameCodeInfoList =
                GameCodeManager.getAndMarkMultipleGameCodes(context, token.phoneNumber)

            if (gameCodeInfoList.isEmpty()) {
                retryCount++
                progressRecorder.recordProcess(
                    "第${retryCount}次获取gameCodeList为空，等待500ms后重试",
                    "WARNING"
                )
                // 添加短暂延迟，避免立即重试
                runBlocking { delay(500) }
            }
        }

        if (gameCodeInfoList.isEmpty()) {
            progressRecorder.recordProcess(
                "多次获取gameCodeList仍为空，停止参加助力券操作",
                "WARNING"
            )
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                tokenIndex,
                "助力券"
            )
            return false
        }

        // 记录获取到的游戏码数量
        progressRecorder.recordProcess("共${gameCodeInfoList.size}个助力券参数，开始参与助力券操作")

        // 处理每一个游戏码
        for (gameCodeInfo in gameCodeInfoList) {
            val prizeId = gameCodeInfo.prizeId
            val gameCode = gameCodeInfo.gameCode
            val phoneNumber = gameCodeInfo.phoneNumber

            try {
                progressRecorder.recordProcess("参与助力券: prizeId=${prizeId}&gameCode=${gameCode}, 手机号=${phoneNumber}")

                // 调用API参与助力
                val boostResult = runBlocking {
                    requestService.coupon.boostCoupon(gameCode)
                }

                when (boostResult) {
                    is RequestResult.Success -> {
                        // 尝试使用详细的响应解析器
                        val detailedResponse =
                            ResponseParserUtils.parseBoostCouponResponse(boostResult.data)

                        if (detailedResponse != null) {
                            if (detailedResponse.code == 0) {
                                successCount++
                                // 获取助力状态信息
                                val boostInfo = detailedResponse.data?.boostCouponBoostDTO
                                val status = boostInfo?.status ?: 0
                                val statusDesc = when (status) {
                                    1 -> "助力成功"
                                    2 -> "助力完成"
                                    else -> "状态未知($status)"
                                }

                                progressRecorder.recordProcess("助力成功: prizeId=${prizeId}&gameCode=${gameCode}, 手机号=${phoneNumber}, 状态=${statusDesc}")

                                // 如果状态为"助力完成"，则从文件中删除该gameCode
                                if (status == 2) {
                                    context.let { ctx ->
                                        val deleted =
                                            GameCodeManager.deleteGameCode(ctx, prizeId, gameCode)
                                        if (deleted) {
                                            progressRecorder.recordProcess("助力完成，已从文件中删除: prizeId=${prizeId}&gameCode=${gameCode}")
                                        } else {
                                            progressRecorder.recordProcess(
                                                "助力完成，但删除错误: prizeId=${prizeId}&gameCode=${gameCode}",
                                                "ERROR"
                                            )
                                        }
                                    }
                                } else {
                                    // 助力成功但未完成，释放gameCode标记
                                    GameCodeManager.releaseGameCode(prizeId, gameCode)
                                }
                            } else {
                                failCount++
                                if ("本期好友助力已结束" == detailedResponse.message) {
                                    context.let { ctx ->
                                        val deleted =
                                            GameCodeManager.deleteGameCode(ctx, prizeId, gameCode)
                                        if (deleted) {
                                            progressRecorder.recordProcess("助力完成，已从文件中删除: prizeId=${prizeId}&gameCode=${gameCode}")
                                        } else {
                                            progressRecorder.recordProcess(
                                                "助力完成，但删除错误: prizeId=${prizeId}&gameCode=${gameCode}",
                                                "ERROR"
                                            )
                                        }
                                    }
                                    progressRecorder.recordProcess(
                                        "本期好友助力已结束，停止参与助力券",
                                        "WARNING"
                                    )
                                    continue // 结束当前循环，处理下一个游戏码
                                } else {
                                    // 释放gameCode标记
                                    GameCodeManager.releaseGameCode(prizeId, gameCode)
                                }

                                if (tokenIndex > 0 && "每日助力次数超过限制" != detailedResponse.message) {
                                    // 记录失败的账号序号
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                        tokenIndex,
                                        "助力券"
                                    )
                                }

                                progressRecorder.recordProcess(
                                    "参与助力错误: prizeId=${prizeId}&gameCode=${gameCode}, 手机号=${phoneNumber}, ${detailedResponse.message}",
                                    "WARNING"
                                )
                            }
                        } else {
                            failCount++
                            // 释放gameCode标记
                            GameCodeManager.releaseGameCode(prizeId, gameCode)
                            progressRecorder.recordProcess(
                                "解析助力响应错误: prizeId=${prizeId}&gameCode=${gameCode}}",
                                "ERROR"
                            )
                            // 记录失败的账号序号
                            if (tokenIndex > 0) {
                                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                    FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                    tokenIndex,
                                    "助力券"
                                )
                            }
                        }
                    }

                    is RequestResult.Error -> {
                        failCount++
                        // 释放gameCode标记
                        GameCodeManager.releaseGameCode(prizeId, gameCode)
                        progressRecorder.recordProcess(
                            "参与助力请求错误: ${boostResult.error.message}",
                            "ERROR"
                        )
                        if (tokenIndex > 0) {
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                                tokenIndex,
                                "助力券"
                            )
                        }
                        Log.e(
                            TAG,
                            "Error boosting coupon with game code: $gameCode",
                            boostResult.error
                        )
                    }
                }

                // 添加间隔，避免请求过快
                runBlocking {
                    delay(1000)
                }

            } catch (e: Exception) {
                failCount++
                // 释放gameCode标记
                GameCodeManager.releaseGameCode(prizeId, gameCode)
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                        tokenIndex,
                        "助力券"
                    )
                }
                progressRecorder.recordProcess("参与助力券时发生异常: ${e.message}", "ERROR")
                Log.e(TAG, "Exception while boosting coupon", e)
            }
        }

        // 记录最终结果
        progressRecorder.recordProcess("助力券参与完成: 成功${successCount}个, 失败${failCount}个")
        return successCount > 0
    }

    private fun handleClearAddressOperation(
        lsAddressList: List<AddressItem>,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        var successCount = 0
        var failCount = 0

        // 遍历所有地址并删除
        for (addressItem in lsAddressList) {
            val addressResult = runBlocking {
                requestService.address.deleteAddress(addressItem.id)
            }

            when (addressResult) {
                is RequestResult.Success -> {
                    val response =
                        ResponseParserUtils.parseResponse<Map<String, Int>>(
                            addressResult.data
                        )
                    if (response != null && response.isSuccess) {
                        successCount++
                        progressRecorder.recordProcess("成功删除地址: ${addressItem.address.area} ${addressItem.address.detail}")
                    } else {
                        failCount++
                        progressRecorder.recordProcess(
                            "删除地址错误: ${response?.errorMessage ?: "未知错误"}",
                            "ERROR"
                        )
                        if (tokenIndex > 0) {
                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                                tokenIndex,
                                "地址清空"
                            )
                        }
                    }
                }

                is RequestResult.Error -> {
                    failCount++
                    progressRecorder.recordProcess(
                        "删除地址异常: ${addressResult.error.message}",
                        "ERROR"
                    )
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                            tokenIndex,
                            "地址清空"
                        )
                    }
                }
            }
        }

        // 记录最终结果
        progressRecorder.recordProcess("地址清空完成: 成功${successCount}个, 失败${failCount}个")
        return successCount > 0
    }

    private fun handleClearCartOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        selectedAddress: AddressItem,
        tokenIndex: Int
    ): Boolean {
        // 获取购物车列表
        val cartResult = runBlocking {
            requestService.cart.getAllCart(selectedAddress.id)
        }

        when (cartResult) {
            is RequestResult.Success -> {
                // 解析购物车响应数据
                val cartResponse =
                    ResponseParserUtils.parseCartResponse(cartResult.data)
                if (cartResponse != null && cartResponse.code == 0) {
                    val cartList = cartResponse.data?.cartlist ?: emptyList()

                    if (cartList.isEmpty()) {
                        progressRecorder.recordProcess("购物车已经为空，停止清空", "WARNING")
                        return true
                    } else {
                        // 清空购物车
                        progressRecorder.recordProcess("开始清空购物车，共${cartList.size}项")
                        val clearResult = runBlocking {
                            requestService.cart.clearCart(cartList, selectedAddress)
                        }

                        when (clearResult) {
                            is RequestResult.Success -> {
                                val response =
                                    ResponseParserUtils.parseResponse<Map<String, Any>>(
                                        clearResult.data
                                    )
                                if (response != null && response.isSuccess) {
                                    progressRecorder.recordProcess("购物车清空成功")
                                    return true
                                } else {
                                    progressRecorder.recordProcess(
                                        "购物车清空错误: ${response?.errorMessage ?: "未知错误"}",
                                        "ERROR"
                                    )
                                    return false
                                }
                            }

                            is RequestResult.Error -> {
                                progressRecorder.recordProcess(
                                    "购物车清空异常: ${clearResult.error.message}",
                                    "ERROR"
                                )
                                if (tokenIndex > 0) {
                                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                                        tokenIndex,
                                        "购物车清空"
                                    )
                                }
                                return false
                            }
                        }
                    }
                } else {
                    progressRecorder.recordProcess(
                        "购物车获取错误: ${cartResponse?.message ?: "未知错误"}",
                        "ERROR"
                    )
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                            tokenIndex,
                            "购物车清空"
                        )
                    }
                    return false
                    Log.e(TAG, "Error getting cart list ${cartResponse?.message ?: "未知错误"}")
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "购物车获取错误: ${cartResult.error.message}",
                    "ERROR"
                )
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                        tokenIndex,
                        "购物车清空"
                    )
                }
                return false
            }
        }
    }

    private fun handleClearOrderOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        // 获取订单列表
        val orderResult = runBlocking {
            requestService.order.getOrderList()
        }

        when (orderResult) {
            is RequestResult.Success -> {
                // 解析订单列表响应数据
                val orderListResponse =
                    ResponseParserUtils.parseOrderListResponse(orderResult.data)
                if (orderListResponse != null && orderListResponse.code == 0) {
                    // 获取订单列表
                    val orders = orderListResponse.data?.orders ?: emptyList()

                    // 筛选需要清空的订单：已取消(6)、已完成(5)、已关闭(16)
                    val needClearOrderList =
                        orders.filter { it.status == 6 || it.status == 5 || it.status == 16 }

                    if (needClearOrderList.isEmpty()) {
                        progressRecorder.recordProcess("没有可清空的订单，停止清空", "WARNING")
                        return true
                    } else {
                        progressRecorder.recordProcess("开始清空订单，共${needClearOrderList.size}个")

                        var clearOrderSuccessCount = 0
                        var clearOrderErrorCount = 0

                        // 遍历所有需要清空的订单并删除
                        for (order in needClearOrderList) {
                            val deleteResult = runBlocking {
                                requestService.order.orderDelete(order.id)
                            }

                            when (deleteResult) {
                                is RequestResult.Success -> {
                                    val response =
                                        ResponseParserUtils.parseOrderDeleteResponse(
                                            deleteResult.data
                                        )
                                    if (response != null && response.code == 0) {
                                        clearOrderSuccessCount++
                                        progressRecorder.recordProcess("成功删除订单: ${order.id}")
                                    } else {
                                        clearOrderErrorCount++
                                        progressRecorder.recordProcess(
                                            "删除订单错误: ${response?.message ?: "未知错误"}",
                                            "ERROR"
                                        )
                                        if (tokenIndex > 0) {
                                            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                                FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                                                tokenIndex,
                                                "订单清空"
                                            )
                                        }
                                    }
                                }

                                is RequestResult.Error -> {
                                    clearOrderErrorCount++
                                    progressRecorder.recordProcess(
                                        "删除订单异常: ${deleteResult.error.message}",
                                        "ERROR"
                                    )
                                    if (tokenIndex > 0) {
                                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                                            FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                                            tokenIndex,
                                            "订单清空"
                                        )
                                    }
                                }
                            }
                        }

                        // 记录最终结果
                        progressRecorder.recordProcess("订单清空完成: 成功${clearOrderSuccessCount}个, 失败${clearOrderErrorCount}个")
                        return clearOrderSuccessCount > 0
                    }
                } else {
                    progressRecorder.recordProcess(
                        "获取订单列表错误: ${orderListResponse?.message ?: "未知错误"}",
                        "ERROR"
                    )
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                            tokenIndex,
                            "订单清空"
                        )
                    }
                    return false
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "获取订单列表异常: ${orderResult.error.message}",
                    "ERROR"
                )
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                        tokenIndex,
                        "订单清空"
                    )
                }
                return false
            }
        }
    }

    private fun handleQueryInfoOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int,
        currentToken: OrderTokenEntity,
        updateOperationStatus: (Boolean) -> Unit
    ): OrderTokenEntity? {
        // 查询账号类型
        val checkAccountTypeResult = runBlocking {
            requestService.user.checkAccountType()
        }

        var needLogin: Boolean = false
        var isNew: Boolean = false
        var accountType: String = ""

        when (checkAccountTypeResult) {
            is RequestResult.Success -> {
                // 使用ResponseParserUtils解析响应数据
                val homepageResponse =
                    ResponseParserUtils.parseHomepageResponse(checkAccountTypeResult.data)

                if (homepageResponse != null && homepageResponse.code == 0) {
                    // 检查是否需要登录
                    needLogin =
                        homepageResponse.data?.loginPopVO?.newPersonLogin == "200"

                    // 检查账户类型
                    if (!needLogin) {
                        accountType = determineAccountType(homepageResponse)

                        progressRecorder.recordProcess("账户类型: $accountType")
                        Log.d(
                            "OrderViewModel",
                            "Account type determined: $accountType"
                        )

                        isNew = accountType == "新人特权"
                    }

                    progressRecorder.recordProcess("账户有效性: ${if (needLogin) "已失效" else "有效"}")
                    Log.d(
                        "OrderViewModel",
                        "Successfully checked account type: needLogin=$needLogin"
                    )

                } else {
                    val errorMsg = homepageResponse?.message ?: "账户类型未知"
                    if(errorMsg.contains("会员专享功能")) {
                        needLogin = true
                    }
                    progressRecorder.recordProcess("账户有效性检查结果: $errorMsg")
                    Log.e("OrderViewModel", "Failed to check account type: $errorMsg")
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "账户类型检查异常: ${checkAccountTypeResult.error.message}",
                    "ERROR"
                )
                // 通过回调更新操作状态为失败
                updateOperationStatus(false)
                Log.e("OrderViewModel", "Error checking account type", checkAccountTypeResult.error)
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                    tokenIndex,
                    "账户类型检查"
                )
                return null
            }
        }

        if (needLogin) {
            progressRecorder.recordProcess("账户已失效，停止查询状态操作", "ERROR")
            // 更新Token
            // 将isLogin设置为false，表示账户已失效
            val updatedToken = currentToken.copy(isLogin = false)
            // 通过回调更新操作状态为成功
            updateOperationStatus(true)
            return updatedToken
        } else {
            progressRecorder.recordProcess("账户有效，继续查询状态操作")
        }

        // 调用API检查卡片购买状态
        val checkCardBuyResult = runBlocking {
            requestService.card.checkCardBuy()
        }

        var checkCardBuy: Boolean = false
        var checkCardBuyStr: String = ""

        when (checkCardBuyResult) {
            is RequestResult.Success -> {
                try {
                    // 使用ResponseParserUtils解析响应数据
                    val response =
                        ResponseParserUtils.parseCardPlaceResponse(checkCardBuyResult.data)
                    if (response != null && response.code == 0) {
                        // 如果响应成功且code为0，设置为"白"
                        checkCardBuy = true
                        checkCardBuyStr = "白"
                        progressRecorder.recordProcess("购买状态: 白")
                        Log.d("OrderViewModel", "Card buy check result: 白, code: 0")
                    } else {
                        // 其他情况设置为黑
                        checkCardBuy = false
                        checkCardBuyStr = "黑"
                        progressRecorder.recordProcess(
                            "购买状态: 黑",
                            "WARNING"
                        )
                        Log.d(
                            "OrderViewModel",
                            "Card buy check failed: ${response?.message}"
                        )
                    }
                } catch (e: Exception) {
                    Log.e("OrderViewModel", "Failed to parse card buy check response", e)
                    progressRecorder.recordProcess(
                        "检查卡片购买状态解析错误: ${e.message}",
                        "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                        tokenIndex,
                        "卡片购买状态检查"
                    )
                    // 通过回调更新操作状态为失败
                    updateOperationStatus(false)
                    return null
                }
            }

            is RequestResult.Error -> {
                Log.e("OrderViewModel", "Error checking card buy", checkCardBuyResult.error)
                progressRecorder.recordProcess(
                    "检查卡片购买状态异常: ${checkCardBuyResult.error.message}",
                    "ERROR"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                    tokenIndex,
                    "卡片购买状态检查"
                )
                // 通过回调更新操作状态为失败
                updateOperationStatus(false)
                return null
            }
        }

        // 调用API检查活动状态
        val checkActivityResult = runBlocking {
            requestService.user.checkActivity()
        }

        var checkActivity: Boolean = false
        var checkActivityStr: String = ""

        when (checkActivityResult) {
            is RequestResult.Success -> {
                try {
                    // 解析响应数据
                    val responseJson = JSONObject(checkActivityResult.data)
                    checkActivityStr = when {
                        // 判断响应码属于哪种状态
                        responseJson.optInt("code") == 410008 ||
                                responseJson.optInt("code") == 22001 ||
                                responseJson.optInt("code") == 20022 -> "白"

                        responseJson.optInt("code") == 22002 -> "黑"

                        else -> "未知"
                    }

                    // 更新活动检查状态
                    checkActivity = checkActivityStr == "白"
                    progressRecorder.recordProcess("活动状态: $checkActivityStr")
                    Log.d(
                        "OrderViewModel",
                        "Activity check result: $checkActivityStr, code: ${
                            responseJson.optInt("code")
                        }"
                    )
                } catch (e: Exception) {
                    Log.e("OrderViewModel", "Failed to parse activity check response", e)
                    progressRecorder.recordProcess(
                        "活动检查解析错误: ${e.message}",
                        "ERROR"
                    )
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                        tokenIndex,
                        "活动检查"
                    )
                    // 通过回调更新操作状态为失败
                    updateOperationStatus(false)
                    return null
                }
            }

            is RequestResult.Error -> {
                Log.e("OrderViewModel", "Error checking activity", checkActivityResult.error)
                progressRecorder.recordProcess(
                    "活动检查异常: ${checkActivityResult.error.message}",
                    "ERROR"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                    tokenIndex,
                    "活动检查"
                )
                // 通过回调更新操作状态为失败
                updateOperationStatus(false)
                return null
            }
        }

        // 通过回调更新操作状态为成功
        updateOperationStatus(true)
        progressRecorder.recordProcess("查询状态完成：$accountType/$checkCardBuyStr/$checkActivityStr")

        // 更新Token，保留当前token的其他属性
        val updatedToken = currentToken.copy(
            isNew = isNew,
            yhCardLimited = !checkCardBuy,
            activityLimited = !checkActivity
        )
        return updatedToken
    }

    private fun handleQueryBalanceOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int,
        currentToken: OrderTokenEntity,
        updateOperationStatus: (Boolean) -> Unit
    ): OrderTokenEntity? {
        // 使用requestService直接调用API获取余额
        val result = runBlocking {
            requestService.card.getCardIndexInfo()
        }

        when (result) {
            is RequestResult.Success -> {
                // 解析响应数据
                val cardIndexInfoResponse =
                    ResponseParserUtils.parseCardIndexInfoResponse(result.data)
                if (cardIndexInfoResponse != null && cardIndexInfoResponse.code == 0) {
                    // 获取余额并记录
                    val balance = (cardIndexInfoResponse.data?.balance ?: "0").toFloat()
                    progressRecorder.recordProcess("查询余额完成: $balance")
                    // 通过回调更新操作状态为成功
                    updateOperationStatus(true)

                    // 更新token，保留当前token的其他属性
                    val updatedToken = currentToken.copy(cardBalance = balance)
                    return updatedToken
                } else {
                    val errorMsg =
                        cardIndexInfoResponse?.message ?: "获取永辉卡余额错误"
                    progressRecorder.recordProcess("查询余额错误: $errorMsg", "ERROR")
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                            tokenIndex,
                            "查询余额"
                        )
                    }
                    // 通过回调更新操作状态为失败
                    updateOperationStatus(false)
                    return null
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess("查询余额异常: ${result.error.message}", "ERROR")
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                        tokenIndex,
                        "查询余额"
                    )
                }
                // 通过回调更新操作状态为失败
                updateOperationStatus(false)
                return null
            }
        }
    }

    private fun handleQueryPointOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int,
        currentToken: OrderTokenEntity,
        updateOperationStatus: (Boolean) -> Unit
    ): OrderTokenEntity? {
        // 调用API获取积分详情
        val result = runBlocking {
            requestService.credit.creditDetail(0)
        }

        when (result) {
            is RequestResult.Success -> {
                // 使用ResponseParserUtils解析响应数据
                val creditResponse = ResponseParserUtils.parseCreditResponse(result.data)
                if (creditResponse != null && creditResponse.code == 0 && creditResponse.data != null) {
                    val data = creditResponse.data

                    val credit = data.credit

                    progressRecorder.recordProcess("积分: $credit")
                    Log.d(
                        "OrderViewModel",
                        "Successfully fetched credit details: ${data.details?.size ?: 0}, page: 1, total: ${data.count}, pageCount: ${data.pagecount}"
                    )

                    // 更新token，保留当前token的其他属性
                    val updatedToken = currentToken.copy(credit = credit)

                    if ((data.details?.isEmpty() != false)) {
                        progressRecorder.recordProcess("积分: 积分记录为空", "WARNING")
                    }

                    // 通过回调更新操作状态为成功
                    updateOperationStatus(true)
                    return updatedToken
                } else {
                    val errorMsg = creditResponse?.message ?: "获取积分错误"
                    progressRecorder.recordProcess("获取积分错误: $errorMsg", "ERROR")
                    Log.e("OrderViewModel", "Failed to get credit details: $errorMsg")
                    // 即使获取详情错误，也标记为成功，因为这不影响主要流程
                    updateOperationStatus(true)
                    return null
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess(
                    "获取积分异常: ${result.error.message}",
                    "ERROR"
                )
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                    tokenIndex,
                    "查询积分"
                )
                Log.e("OrderViewModel", "Error fetching credit details", result.error)
                // 通过回调更新操作状态为失败
                updateOperationStatus(false)
                return null
            }
        }
    }

    private fun handleQueryOrderOperation(
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        tokenIndex: Int
    ): Boolean {
        // 使用requestService直接调用API获取订单列表
        val result = runBlocking {
            requestService.order.getOrderList()
        }

        when (result) {
            is RequestResult.Success -> {
                // 解析响应数据
                val orderListResponse =
                    ResponseParserUtils.parseOrderListResponse(result.data)
                if (orderListResponse != null && orderListResponse.code == 0) {
                    // 获取订单列表
                    val orders = orderListResponse.data?.orders ?: emptyList()

                    // 筛选订单关键参数到数据储存类，方便后续筛选导出
                    formatOrderListToDB(orders, requestService.getPhoneNumber())

                    // 计算订单状态统计信息
                    val orderListInfo = orders.groupBy { it.statusmsg }
                        .map { "${it.key}${it.value.size}" }.joinToString("")

                    // 格式化订单状态统计信息
                    val formattedInfo =
                        if (orderListInfo.isNotEmpty() && !orderListInfo.startsWith("订单")) {
                            "订单$orderListInfo"
                        } else {
                            orderListInfo
                        }

                    progressRecorder.recordProcess("查询订单完成: ${formattedInfo.ifEmpty { "订单列表为空" }}")
                    return true
                } else {
                    val errorMsg = orderListResponse?.message ?: "获取订单列表错误"
                    progressRecorder.recordProcess("查询订单错误: $errorMsg", "ERROR")
                    if (tokenIndex > 0) {
                        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                            FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                            tokenIndex,
                            "查询订单"
                        )
                    }
                    return false
                }
            }

            is RequestResult.Error -> {
                progressRecorder.recordProcess("查询订单异常: ${result.error.message}", "ERROR")
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACCOUNT_OPERATION,
                        tokenIndex,
                        "查询订单"
                    )
                }
                return false
            }
        }
    }

    /**
     * 格式化订单列表数据到数据库
     * 提取订单关键参数到数据储存类，方便后续筛选导出
     *
     * @param orders 订单列表
     * @param phoneNumber 手机号
     */
    private fun formatOrderListToDB(orders: List<Order>, phoneNumber: String) {
        if (orders.isEmpty()) {
            Log.d(TAG, "No orders to format to DB")
            return
        }

        Log.d(TAG, "Formatting ${orders.size} orders to DB")

        try {
            // 创建订单数据列表
            val orderDataList = orders.map { order ->
                // 提取商品信息
                val productInfo = order.products.joinToString(";") { product ->
                    "${product.title}(${product.num / 100}${product.unit})"
                }

                // 提取支付时间
                val payTime = if (order.timeinfo.pay > 0) {
                    DateUtils.formatDateTime(order.timeinfo.pay)
                } else {
                    ""
                }

                // 提取完成时间
                val completeTime = if (order.timeinfo.complete > 0) {
                    DateUtils.formatDateTime(order.timeinfo.complete)
                } else {
                    ""
                }

                // 构建订单数据对象
                OrderData(
                    phoneNumber = phoneNumber,
                    orderId = order.id,
                    status = order.status,
                    statusMsg = order.statusmsg,
                    payAmount = order.totalpayment / 100.0, // 转换为元
                    payType = order.paytypename,
                    shopId = order.shopid,
                    shopName = order.seller.title,
                    productInfo = productInfo,
                    payTime = payTime,
                    completeTime = completeTime,
                    deliveryMode = if (order.deliverymode == 1) "配送" else "自提",
                )
            }

            // 创建一个订单数据类储存类，用于存储订单关键信息，不使用数据库
            OrderDataCacheManager.saveOrderData(phoneNumber, orderDataList)
            Log.d(TAG, "Saved ${orderDataList.size} orders to memory cache for phone: $phoneNumber")

        } catch (e: Exception) {
            Log.e(TAG, "Error formatting orders to DB", e)
        }
    }
} 
