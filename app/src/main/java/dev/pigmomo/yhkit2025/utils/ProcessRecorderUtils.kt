package dev.pigmomo.yhkit2025.utils

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.data.repository.LogRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 用于管理和暴露进度记录的单例对象。
 * 它维护一个进度记录的映射，并通过StateFlow提供响应式更新。
 */
object ProgressManager {
    // 用于存储每个账号的进度记录
    private val progressRecords = mutableMapOf<String, String>()
    private val progressRecordsLock = Any()

    // 添加一个StateFlow来包装progressRecords，用于UI更新
    private val _progressRecordsFlow = MutableStateFlow<Map<String, String>>(emptyMap())
    val progressRecordsFlow: StateFlow<Map<String, String>> = _progressRecordsFlow.asStateFlow()

    // 协程作用域，用于异步数据库操作
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    // 日志仓库，延迟初始化
    private var logRepository: LogRepositoryImpl? = null

    /**
     * 初始化日志仓库
     * @param context 应用上下文
     */
    fun initialize(context: Context) {
        if (logRepository == null) {
            try {
                val logDao = AppDatabase.getDatabase(context).logDao()
                logRepository = LogRepositoryImpl(logDao)
                Log.d("ProgressManager", "Log repository initialized successfully")
            } catch (e: Exception) {
                Log.e("ProgressManager", "Failed to initialize log repository", e)
            }
        }
    }

    /**
     * 更新给定tokenUid的进度记录。
     *
     * @param tokenUid 令牌的唯一标识符。
     * @param message 要记录的消息。
     * @param tag 日志标签，默认为"ProcessRecorder"
     * @param logLevel 日志级别，默认为"INFO"
     * @param saveToDb 是否保存到数据库，默认为true
     * @param phoneNumber 关联的手机号码，默认为空字符串
     */
    fun updateRecord(
        tokenUid: String,
        message: String,
        tag: String = "ProcessRecorder",
        logLevel: String = "INFO",
        saveToDb: Boolean = true,
        phoneNumber: String = ""
    ) {
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords[tokenUid] = message
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        // 保存到数据库
        if (saveToDb && logRepository != null) {
            coroutineScope.launch {
                try {
                    logRepository?.saveLog(tokenUid, message, tag, logLevel, phoneNumber)
                    Log.d("ProgressManager", "Log saved to database successfully")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to save log to database", e)
                }
            }
        }
    }

    /**
     * 从数据库同步最新的记录到进度记录映射中
     * 仅当progressRecords为空时才进行同步，避免覆盖已有的记录
     * @param orderTokens 需要同步的令牌列表
     * @param batchSize 每批处理的令牌数量，默认为50
     * @param timeoutPerBatch 每批处理的超时时间（毫秒），默认为5000ms
     * @param delayBetweenBatches 批次之间的延迟（毫秒），默认为100ms
     */
    fun syncLatestRecordFromDbByOrderTokens(
        context: Context, 
        orderTokens: List<OrderTokenEntity>,
        batchSize: Int = 50,
        timeoutPerBatch: Long = 5000,
        delayBetweenBatches: Long = 100
    ) {
        // 检查progressRecords是否为空
        val isEmpty = synchronized(progressRecordsLock) {
            progressRecords.isEmpty()
        }

        // 如果progressRecords为空，则初始化日志仓库
        if (isEmpty && logRepository == null) {
            initialize(context)
        }
        
        // 如果progressRecords为空，才进行同步
        if (isEmpty && logRepository != null) {
            coroutineScope.launch {
                try {
                    Log.d("ProgressManager", "Syncing latest records from database for ${orderTokens.size} tokens")
                    val allSyncedRecords = mutableMapOf<String, String>()
                    
                    // 批处理参数
                    val totalTokens = orderTokens.size
                    var processedCount = 0
                    var errorCount = 0
                    
                    // 分批处理令牌
                    orderTokens.chunked(batchSize).forEachIndexed { batchIndex, tokenBatch ->
                        try {
                            // 每批次的临时记录
                            val batchRecords = mutableMapOf<String, String>()
                            
                            // 使用withTimeout确保每批处理不会超时
                            kotlinx.coroutines.withTimeoutOrNull(timeoutPerBatch) {
                                // 处理当前批次
                                tokenBatch.forEach { orderToken ->
                                    try {
                                        val latestRecord = logRepository?.getLatestLogByToken(orderToken.uid)
                                        if (latestRecord != null) {
                                            batchRecords[orderToken.uid] = latestRecord.message
                                            allSyncedRecords[orderToken.uid] = latestRecord.message
                                            Log.d("ProgressManager", "Synced record for token ${orderToken.uid}: ${latestRecord.message}")
                                        }
                                    } catch (e: Exception) {
                                        errorCount++
                                        Log.e("ProgressManager", "Error syncing record for token ${orderToken.uid}", e)
                                    }
                                }
                            } ?: run {
                                // 超时处理
                                Log.w("ProgressManager", "Batch ${batchIndex + 1} processing timed out after ${timeoutPerBatch}ms")
                            }
                            
                            processedCount += tokenBatch.size
                            Log.d("ProgressManager", "Processed batch ${batchIndex + 1}: ${tokenBatch.size} tokens, total progress: $processedCount/$totalTokens")
                            
                            // 每批处理完成后更新progressRecords和StateFlow
                            if (batchRecords.isNotEmpty()) {
                                val newMapSnapshot = synchronized(progressRecordsLock) {
                                    progressRecords.putAll(batchRecords)
                                    progressRecords.toMap()
                                }
                                _progressRecordsFlow.value = newMapSnapshot
                            }
                            
                            // 添加延迟，避免过度消耗系统资源
                            if (batchIndex < orderTokens.chunked(batchSize).size - 1) {
                                kotlinx.coroutines.delay(delayBetweenBatches)
                            }
                        } catch (e: Exception) {
                            Log.e("ProgressManager", "Error processing batch ${batchIndex + 1}", e)
                        }
                    }
                    
                    Log.d("ProgressManager", "Synced ${allSyncedRecords.size} records from database with $errorCount errors")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to sync records from database", e)
                }
            }
        } else {
            Log.d("ProgressManager", "Skipping database sync as records are not empty or repository is null")
        }
    }

    /**
     * 清除指定tokenUid的进度记录。
     * @param tokenUid 令牌的唯一标识符。
     * @param deleteFromDb 是否从数据库中删除，默认为false
     */
    fun clearRecord(tokenUid: String, deleteFromDb: Boolean = false) {
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords.remove(tokenUid)
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        // 从数据库删除
        if (deleteFromDb && logRepository != null) {
            coroutineScope.launch {
                try {
                    logRepository?.deleteLogsByToken(tokenUid)
                    Log.d("ProgressManager", "Logs deleted from database successfully")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to delete logs from database", e)
                }
            }
        }
    }

    /**
     * 清除所有进度记录。
     * @param clearDb 是否清除数据库，默认为false
     */
    fun clearAllRecords(clearDb: Boolean = false) {
        val newMapSnapshot = synchronized(progressRecordsLock) {
            progressRecords.clear()
            progressRecords.toMap()
        }
        _progressRecordsFlow.value = newMapSnapshot

        // 清空数据库
        if (clearDb && logRepository != null) {
            coroutineScope.launch {
                try {
                    logRepository?.clearAllLogs()
                    Log.d("ProgressManager", "All logs cleared from database successfully")
                } catch (e: Exception) {
                    Log.e("ProgressManager", "Failed to clear logs from database", e)
                }
            }
        }
    }
}

/**
 * 进度记录器类
 * 用于记录处理过程并保存在内存中的临时记录
 * @param tokenUid 与此记录器关联的令牌的唯一标识符。
 * @param phoneNumber 关联的手机号码，用于标识用户。
 * @param tag 用于日志记录的日志标签。默认为"ProcessRecorder"。
 * @param logLevelDefault 日志级别。默认为"INFO"。
 * @param saveToDb 是否保存到数据库。默认为true。
 * @param context 应用上下文，用于数据库初始化。
 */
class ProcessRecorder(
    private val tokenUid: String,
    private val phoneNumber: String,
    private val tag: String = "ProcessRecorder",
    private val logLevelDefault: String = "INFO",
    private val saveToDb: Boolean = true,
    context: Context? = null
) {
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.CHINA)
    private val currentTime: String
        get() = dateFormatter.format(Date())

    init {
        // 初始化日志仓库，如果提供了上下文
        context?.let {
            ProgressManager.initialize(it.applicationContext)
        }
    }

    /**
     * 记录处理步骤
     * @param message 步骤描述
     */
    fun recordProcess(message: String, logLevel: String = logLevelDefault) {
        val formattedMessage = "$currentTime $message"
        Log.d(tag, "Token $tokenUid, Phone $phoneNumber: $formattedMessage")
        ProgressManager.updateRecord(tokenUid, formattedMessage, tag, logLevel, saveToDb, phoneNumber)
    }
} 